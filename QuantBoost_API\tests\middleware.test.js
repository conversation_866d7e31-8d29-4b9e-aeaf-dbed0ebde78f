const request = require('supertest');
const { app, serverInstance } = require('../index');

describe('API Key Validation Middleware', () => {
    afterAll((done) => {
        if (serverInstance) {
            serverInstance.close(done);
        } else {
            done();
        }
    });

    it('should allow access with a valid API key', async () => {
        const apiKey = process.env.SUPABASE_KEY; 
        if (!apiKey) {
            console.warn('[Test Skip] SUPABASE_KEY not set, skipping valid API key test for middleware.');
            // If using Jest, you might prefer:
            // expect.assertions(0); // Or a specific number if other assertions exist
            // return;
            // For simplicity, just returning if not set.
            return;
        }
        const response = await request(app)
            .get('/v1/some-protected-route') 
            .set('x-api-key', apiKey);
        
        expect(response.status).not.toBe(401); 

        if (response.status === 404) {
            console.warn("[Test Info] Middleware test passed API key check, but /v1/some-protected-route resulted in 404. This is OK if the route isn't defined or is handled by a later router.");
        }
        // Add more specific status checks if /v1/some-protected-route is expected to be a valid, simple endpoint (e.g., expect(response.status).toBe(200);)
    });

    it('should deny access with an invalid API key', async () => {
        const response = await request(app)
            .get('/v1/some-protected-route')
            .set('x-api-key', 'invalid-api-key');
        expect(response.status).toBe(401);
        expect(response.body.error.message).toBe('Invalid API Key');
    });

    it('should deny access if no API key is provided', async () => {
        const response = await request(app)
            .get('/v1/some-protected-route');
        expect(response.status).toBe(401);
        expect(response.body.error.message).toBe('API Key Required');
    });
});
