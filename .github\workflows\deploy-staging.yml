name: Deploy Frontend to Azure App Service (Staging)

on:
  push:
    branches: [ main, staging ]
    paths:
      - 'QuantBoost_Frontend/**'
  workflow_dispatch:  # Allow manual deployment

env:
  AZURE_WEBAPP_NAME: app-quantboost-frontend-staging
  AZURE_WEBAPP_PACKAGE_PATH: './QuantBoost_Frontend'
  NODE_VERSION: '18'

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    name: Build and Deploy Job

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'QuantBoost_Frontend/package-lock.json'

    - name: Install dependencies
      run: |
        cd QuantBoost_Frontend
        npm ci

    - name: Build application
      run: |
        cd QuantBoost_Frontend
        npm run build
      env:
        # Build-time environment variables
        NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: ${{ secrets.STRIPE_PUBLISHABLE_KEY }}
        NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.SUPABASE_ANON_KEY }}
        NEXT_PUBLIC_BASE_URL: "https://app-quantboost-frontend-staging.azurewebsites.net"

    - name: Deploy to Azure App Service
      uses: azure/webapps-deploy@v2
      with:
        app-name: ${{ env.AZURE_WEBAPP_NAME }}
        publish-profile: ${{ secrets.AZURE_WEBAPP_PUBLISH_PROFILE_STAGING }}
        package: ${{ env.AZURE_WEBAPP_PACKAGE_PATH }}
