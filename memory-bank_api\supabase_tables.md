---
title: QuantBoost Licensing API - Supabase Table Schemas
purpose: Outlines the schemas for the tables in the Supabase `public` schema for the QuantBoost API project.
last_updated: 2025-05-22T00:00:00.000Z # Update this as you progress
tags: ["database", "schema", "supabase", "public"]
project_ref: izoutrnsxaaoueljiimu
---

# Supabase Table Schemas (as of 2025-05-22)

This document outlines the schemas for the tables in the Supabase `public` schema for the QuantBoost API project (`izoutrnsxaaoueljiimu`).
It reflects decisions on `NOT NULL` constraints and default values.

**Key for Default Values:**
- `NOW()`: Current timestamp.
- `uuid_generate_v4()`: Generates a new UUID v4.
- Specific values like `'inactive'::license_status` or `TRUE` are literal defaults.

## `license_activations`

- RLS Enabled: true
- RLS Forced: false

| Column Name         | Data Type                | Default Value        | Nullable | Unique | Comment |
|---------------------|--------------------------|----------------------|----------|--------|---------|
| `id`                | uuid                     | `uuid_generate_v4()` | **false**| PK     |         |
| `license_id`        | uuid                     |                      | **false**|        | FK to `licenses.id` |
| `machine_id`        | text                     |                      | **false**|        | Unique per license if `(license_id, machine_id)` is unique constraint |
| `activated_at`      | timestamp with time zone | `NOW()`              | **false**|        | Time of first activation for this record. |
| `last_validated_at` | timestamp with time zone | `NOW()`              | **false**|        | Last time this activation was checked/used. |
| `is_active`         | boolean                  | `TRUE`               | **false**|        | Whether this specific machine activation is currently active. |
| `email`             | text                     |                      | true     |        | Email of the user at the time of activation (denormalized). |

**Relationships:**
- `license_id` references `licenses(id)` `ON DELETE CASCADE` (Recommended: if a license is deleted, its activations should also be deleted).

**Indexes (Recommended):**
- Index on `license_id`.
- Unique constraint on `(license_id, machine_id)` if a license can only be active on one machine at a time, or if you want to prevent duplicate activation records for the same machine/license pair.

## `licenses`

- RLS Enabled: true
- RLS Forced: false

| Column Name         | Data Type                | Default Value        | Nullable | Unique | Comment |
|---------------------|--------------------------|----------------------|----------|--------|---------|
| `id`                | uuid                     | `uuid_generate_v4()` | **false**| PK     |         |
| `user_id`           | uuid                     |                      | true     |        | FK to `profiles.id`. User this license is assigned to. |
| `subscription_id`   | uuid                     |                      | true     |        | FK to `subscriptions.id`. Subscription providing this license (NULL for system trials). |
| `product_id`        | text                     |                      | **false**|        | Identifier for the product this license is for. |
| `status`            | USER-DEFINED (license_status) | `'unassigned'::license_status` | **false**|        | Enum: "active", "inactive", "canceled", "trial_active", "expired", "graceperiod", "unassigned", "assigned", "revoked". Default 'unassigned' for new pool licenses. |
| `created_at`        | timestamp with time zone | `NOW()`              | **false**|        |         |
| `updated_at`        | timestamp with time zone | `NOW()`              | **false**|        | Auto-updates via trigger. |
| `email`             | text                     |                      | true     |        | Email invited/assigned to, before `user_id` might be known. |
| `trial_start_date`  | timestamp with time zone |                      | true     |        |         |
| `trial_expiry_date` | timestamp with time zone |                      | true     |        |         |
| `license_tier`      | text                     |                      | true     |        | e.g., "Basic", "Pro", "Enterprise". |
| `license_key`       | text                     | `uuid_generate_v4()` | **false**| true   | User-facing or system-used license key. |
| `expiry_date`       | timestamp with time zone |                      | true     |        | Expiry for paid licenses. |
| `max_activations`   | integer                  | `1`                  | **false**|        | Default number of concurrent machine activations allowed. |
| `assigned_at`       | timestamp with time zone |                      | true     |        | Timestamp when license was last assigned to a user. |

**Relationships:**
- `user_id` references `profiles(id)` `ON DELETE SET NULL` (Recommended: if a profile is deleted, unassign the license).
- `subscription_id` references `subscriptions(id)` `ON DELETE SET NULL` (Recommended: if a subscription is deleted, licenses become unlinked but might remain, or `ON DELETE CASCADE` if licenses should be removed too).
- `id` is referenced by `license_activations(license_id)`.
- `id` is referenced by `license_events(license_id)`.

**Indexes (Recommended):**
- Index on `user_id`.
- Index on `subscription_id`.
- Index on `product_id`.
- Index on `status`.
- Index on `email`.

**Triggers (Recommended):**
- Trigger on `updated_at` to set to `NOW()` on `UPDATE`.

## `profiles`

- RLS Enabled: true
- RLS Forced: false

| Column Name           | Data Type                | Default Value | Nullable | Unique | Comment |
|-----------------------|--------------------------|---------------|----------|--------|---------|
| `id`                  | uuid                     |               | **false**| PK     | FK to `auth.users.id`. Should match `auth.users.id`. |
| `full_name`           | text                     |               | true     |        |         |
| `email`               | text                     |               | **false**| true   | Should match `auth.users.email`. |
| `stripe_customer_id`  | text                     |               | true     | true   |         |
| `is_enterprise_admin` | boolean                  | `FALSE`       | **false**|        |         |
| `created_at`          | timestamp with time zone | `NOW()`       | **false**|        |         |
| `updated_at`          | timestamp with time zone | `NOW()`       | **false**|        | Auto-updates via trigger. |

**Relationships:**
- `id` references `auth.users(id)` `ON DELETE CASCADE` (Critical: if auth user is deleted, profile should be too).
- `id` is referenced by `licenses(user_id)`.
- `id` is referenced by `subscriptions(user_id)`.

**Triggers (Recommended):**
- Trigger on `auth.users` `AFTER INSERT` to create a corresponding `profiles` row.
- Trigger on `updated_at` to set to `NOW()` on `UPDATE`.

## `subscriptions`

- RLS Enabled: true
- RLS Forced: false

| Column Name              | Data Type                | Default Value        | Nullable | Unique | Comment |
|--------------------------|--------------------------|----------------------|----------|--------|---------|
| `id`                     | uuid                     | `uuid_generate_v4()` | **false**| PK     |         |
| `user_id`                | uuid                     |                      | **false**|        | FK to `profiles.id`. Owner of the subscription. |
| `stripe_subscription_id` | text                     |                      | true     | true   | Stripe's ID for this subscription. |
| `status`                 | USER-DEFINED (subscription_status) |             | **false**|        | Enum: "trialing", "active", "past_due", "canceled", "incomplete". |
| `quantity`               | integer                  | `1`                  | **false**|        | Number of seats/licenses in this subscription. |
| `current_period_start`   | timestamp with time zone |                      | true     |        | From Stripe. |
| `current_period_end`     | timestamp with time zone |                      | true     |        | From Stripe. Primary expiry driver for associated licenses. |
| `trial_start_date`       | timestamp with time zone |                      | true     |        | Renamed from `trial_start` for consistency. |
| `trial_end_date`         | timestamp with time zone |                      | true     |        | Renamed from `trial_end` for consistency. |
| `cancel_at_period_end`   | boolean                  | `FALSE`              | **false**|        | From Stripe. |
| `canceled_at`            | timestamp with time zone |                      | true     |        | From Stripe. |
| `created_at`             | timestamp with time zone | `NOW()`              | **false**|        |         |
| `updated_at`             | timestamp with time zone | `NOW()`              | **false**|        | Auto-updates via trigger. |

**Relationships:**
- `user_id` references `profiles(id)` `ON DELETE RESTRICT` (Recommended: prevent deleting a profile if they have active subscriptions, or `CASCADE` if subscriptions should be deleted too).
- `id` is referenced by `licenses(subscription_id)`.

**Indexes (Recommended):**
- Index on `user_id`.
- Index on `stripe_subscription_id`.
- Index on `status`.

**Triggers (Recommended):**
- Trigger on `updated_at` to set to `NOW()` on `UPDATE`.

## `license_events`

- RLS Enabled: true
- RLS Forced: false

| Column Name    | Data Type                | Default Value        | Nullable | Unique | Comment |
|----------------|--------------------------|----------------------|----------|--------|---------|
| `id`           | uuid                     | `uuid_generate_v4()` | **false**| PK     |         |
| `event_type`   | text                     |                      | **false**|        | e.g., "validation_attempt", "activation_success". |
| `license_id`   | uuid                     |                      | true     |        | FK to `licenses.id`. Event might not always have a license. |
| `machine_id`   | text                     |                      | **false**|        | Device identifier involved in the event. |
| `email`        | text                     |                      | true     |        | Email associated with the event (user or license email). |
| `status`       | text                     |                      | true     |        | Outcome or specific status of the event, e.g., "success_active", "failure_invalid_key". |
| `created_at`   | timestamp with time zone | `NOW()`              | **false**|        | Timestamp of the event. |

**Relationships:**
- `license_id` references `licenses(id)` `ON DELETE SET NULL` (Recommended: if a license is deleted, keep the event but unlink it).

**Indexes (Recommended):**
- Index on `event_type`.
- Index on `license_id`.
- Index on `email`.
- Index on `created_at`.

---