// Test script to verify Stripe webhook endpoint functionality
const crypto = require('crypto');

// Test webhook endpoint with a simulated Stripe event
async function testWebhookEndpoint() {
  const webhookSecret = 'whsec_y84fUcgTM5taGcLwNBdOsucbsi6Suy8u';
  const endpointUrl = 'https://purple-glacier-0ab50190f.1.azurestaticapps.net/api/webhooks/stripe';
  
  // Create a test event payload
  const testEvent = {
    id: 'evt_test_webhook_' + Date.now(),
    object: 'event',
    type: 'customer.subscription.created',
    created: Math.floor(Date.now() / 1000),
    data: {
      object: {
        id: 'sub_test_' + Date.now(),
        object: 'subscription',
        customer: 'cus_test_customer',
        status: 'active',
        items: {
          data: [{
            price: {
              id: 'price_1RCQeBE6FvhUKV1bUN94Oihf'
            },
            quantity: 1
          }]
        },
        current_period_start: Math.floor(Date.now() / 1000),
        current_period_end: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60),
        created: Math.floor(Date.now() / 1000),
        cancel_at_period_end: false,
        canceled_at: null,
        trial_start: null,
        trial_end: null
      }
    }
  };

  const payload = JSON.stringify(testEvent);
  const timestamp = Math.floor(Date.now() / 1000);
  
  // Create Stripe signature
  const signedPayload = timestamp + '.' + payload;
  const signature = crypto
    .createHmac('sha256', webhookSecret)
    .update(signedPayload, 'utf8')
    .digest('hex');
  
  const stripeSignature = `t=${timestamp},v1=${signature}`;

  console.log('🧪 Testing webhook endpoint...');
  console.log('📍 Endpoint:', endpointUrl);
  console.log('📦 Event ID:', testEvent.id);
  console.log('🔐 Signature:', stripeSignature);

  try {
    const response = await fetch(endpointUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'stripe-signature': stripeSignature
      },
      body: payload
    });

    console.log('📊 Response Status:', response.status);
    console.log('📋 Response Headers:', Object.fromEntries(response.headers.entries()));
    
    const responseText = await response.text();
    console.log('📄 Response Body:', responseText);

    if (response.ok) {
      console.log('✅ Webhook endpoint test PASSED');
      return true;
    } else {
      console.log('❌ Webhook endpoint test FAILED');
      return false;
    }
  } catch (error) {
    console.error('💥 Error testing webhook:', error);
    return false;
  }
}

// Test with invalid signature
async function testInvalidSignature() {
  const endpointUrl = 'https://purple-glacier-0ab50190f.1.azurestaticapps.net/api/webhooks/stripe';
  
  console.log('\n🧪 Testing invalid signature handling...');
  
  try {
    const response = await fetch(endpointUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'stripe-signature': 'invalid_signature'
      },
      body: JSON.stringify({ test: 'invalid' })
    });

    console.log('📊 Response Status:', response.status);
    
    if (response.status === 400) {
      console.log('✅ Invalid signature test PASSED (correctly rejected)');
      return true;
    } else {
      console.log('❌ Invalid signature test FAILED (should return 400)');
      return false;
    }
  } catch (error) {
    console.error('💥 Error testing invalid signature:', error);
    return false;
  }
}

// Run tests
async function runTests() {
  console.log('🚀 Starting webhook endpoint tests...\n');
  
  const test1 = await testWebhookEndpoint();
  const test2 = await testInvalidSignature();
  
  console.log('\n📊 Test Results:');
  console.log('✅ Valid webhook test:', test1 ? 'PASSED' : 'FAILED');
  console.log('✅ Invalid signature test:', test2 ? 'PASSED' : 'FAILED');
  
  if (test1 && test2) {
    console.log('\n🎉 All webhook tests PASSED! Endpoint is working correctly.');
  } else {
    console.log('\n❌ Some tests FAILED. Check the logs above for details.');
  }
}

runTests();
