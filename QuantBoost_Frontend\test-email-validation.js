// Test script to verify email validation in checkout flow
const fetch = require('node-fetch');

// Test the create-payment-intent API with various email scenarios
async function testEmailValidation() {
  const baseUrl = 'http://localhost:3000'; // Adjust for your local dev server
  const endpoint = `${baseUrl}/api/checkout/create-payment-intent`;
  
  const testCases = [
    {
      name: 'Valid email',
      email: '<EMAIL>',
      expectedStatus: 200,
      shouldSucceed: true
    },
    {
      name: 'Guest email (should be rejected)',
      email: '<EMAIL>',
      expectedStatus: 400,
      shouldSucceed: false
    },
    {
      name: 'Invalid email format',
      email: 'invalid-email',
      expectedStatus: 400,
      shouldSucceed: false
    },
    {
      name: 'Empty email',
      email: '',
      expectedStatus: 400,
      shouldSucceed: false
    },
    {
      name: 'Missing email',
      email: null,
      expectedStatus: 400,
      shouldSucceed: false
    }
  ];

  console.log('🧪 Testing email validation in create-payment-intent API...\n');

  for (const testCase of testCases) {
    console.log(`Testing: ${testCase.name}`);
    console.log(`Email: ${testCase.email || 'null'}`);
    
    try {
      const payload = {
        priceId: 'price_1RCQeBE6FvhUKV1bUN94Oihf', // Valid test price ID
        userId: null,
        quantity: 1,
        customerInfo: {
          firstName: 'Test',
          lastName: 'User',
          phone: '',
          addressLine1: '123 Test St',
          addressLine2: '',
          city: 'Test City',
          state: 'CA',
          postalCode: '12345',
          country: 'US',
        }
      };

      // Only add email if it's not null (to test missing email case)
      if (testCase.email !== null) {
        payload.email = testCase.email;
      }

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const responseData = await response.json();
      
      console.log(`Status: ${response.status}`);
      console.log(`Response: ${JSON.stringify(responseData, null, 2)}`);
      
      // Validate the response
      const statusMatches = response.status === testCase.expectedStatus;
      const successMatches = testCase.shouldSucceed ? response.ok : !response.ok;
      
      if (statusMatches && successMatches) {
        console.log('✅ Test PASSED\n');
      } else {
        console.log('❌ Test FAILED');
        console.log(`Expected status: ${testCase.expectedStatus}, Got: ${response.status}`);
        console.log(`Expected success: ${testCase.shouldSucceed}, Got: ${response.ok}\n`);
      }
      
    } catch (error) {
      console.log(`❌ Test FAILED with error: ${error.message}\n`);
    }
  }
}

// Test webhook email processing
async function testWebhookEmailProcessing() {
  console.log('🧪 Testing webhook email processing...\n');
  
  // This would require setting up a test webhook endpoint
  // For now, we'll just log that this test needs to be implemented
  console.log('⚠️ Webhook email processing test requires live webhook setup');
  console.log('To test manually:');
  console.log('1. Create a test subscription with a real email');
  console.log('2. Check Azure App Service logs for email processing');
  console.log('3. <NAME_EMAIL> entries are created in Supabase\n');
}

// Run tests
async function runTests() {
  console.log('🚀 Starting email validation tests...\n');
  
  await testEmailValidation();
  await testWebhookEmailProcessing();
  
  console.log('✅ All tests completed!');
}

// Check if running directly
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = {
  testEmailValidation,
  testWebhookEmailProcessing,
  runTests
};
