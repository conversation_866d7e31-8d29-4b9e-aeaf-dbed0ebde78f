import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function POST(req: NextRequest) {
  // Create Supabase client inside the function to avoid build-time issues
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_KEY!
  );
  try {
    const { email, paymentIntentId } = await req.json();

    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }

    // Verify that this email has a recent successful payment
    // This is a security measure to prevent unauthorized login attempts
    if (paymentIntentId) {
      // You could add additional verification here by checking Stripe
      // For now, we'll trust that the payment was successful since this
      // endpoint should only be called from the checkout success flow
    }

    // Check if user exists in auth.users
    const { data: { users }, error: listError } = await supabase.auth.admin.listUsers();
    if (listError) {
      console.error('Error listing users:', listError);
      return NextResponse.json({ error: 'Authentication service error' }, { status: 500 });
    }

    const existingUser = users.find(user => user.email === email);
    if (!existingUser) {
      return NextResponse.json({ error: 'User account not found' }, { status: 404 });
    }

    // Generate a magic link for automatic login
    const { data, error } = await supabase.auth.admin.generateLink({
      type: 'magiclink',
      email: email,
      options: {
        redirectTo: `${process.env.NEXT_PUBLIC_BASE_URL}/dashboard?payment=success`
      }
    });

    if (error) {
      console.error('Error generating magic link:', error);
      return NextResponse.json({ error: 'Could not generate login link' }, { status: 500 });
    }

    return NextResponse.json({ 
      success: true, 
      loginUrl: data.properties?.action_link,
      message: 'Login link generated successfully'
    });

  } catch (error) {
    console.error('Error in post-payment login:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
