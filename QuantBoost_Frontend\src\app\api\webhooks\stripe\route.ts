// src/app/api/webhooks/stripe/route.ts

import { NextResponse } from 'next/server';
import <PERSON><PERSON> from 'stripe';
import { createClient } from '@supabase/supabase-js';

function toIsoDate(secOrMs: number | null | undefined): string | null {
  if (!secOrMs) return null;
  return new Date(secOrMs * 1000).toISOString();
}
function generateLicenseKey(): string {
  return crypto.randomUUID();
}

export async function POST(req: Request) {
  // Initialize clients inside the function to avoid build-time issues
  const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
    apiVersion: '2025-03-31.basil',
  });
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET!;
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  );

  const rawBody = await req.text();
  const signature = req.headers.get('stripe-signature');
  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(rawBody, signature!, webhookSecret);
    console.log('✅ Stripe event verified:', event.type, 'ID:', event.id);
  } catch (err) {
    console.error('❌ Stripe signature error:', err);
    return new NextResponse('Webhook signature failed', { status: 400 });
  }

  // Check for duplicate events (idempotency protection)
  const { data: existingEvent } = await supabase
    .from('webhook_events')
    .select('id')
    .eq('stripe_event_id', event.id)
    .maybeSingle();

  if (existingEvent) {
    console.log('⚠️ Duplicate event detected, skipping:', event.id);
    return new NextResponse('Event already processed', { status: 200 });
  }

  // Log the event for tracking (will update status after processing)
  try {
    const now = new Date();
    const { data: eventLog, error: eventLogError } = await supabase
      .from('webhook_events')
      .upsert({
        stripe_event_id: event.id,
        event_type: event.type,
        processed_at: now.toISOString(),
      }, {
        onConflict: 'stripe_event_id'
      })
      .select()
      .single();

    if (eventLogError) {
      console.error('Failed to log webhook event:', {
        error: eventLogError,
        eventId: event.id,
        eventType: event.type,
        timestamp: now.toISOString(),
        supabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
        supabaseKey: !!process.env.SUPABASE_SERVICE_KEY
      });
      // Don't fail the webhook for logging issues - continue processing
      console.warn('Continuing webhook processing despite logging failure');
    } else {
      console.log('✅ Webhook event logged successfully:', event.id);
    }
  } catch (loggingError) {
    console.error('Exception during webhook event logging:', loggingError);
    // Don't fail the webhook for logging issues - continue processing
    console.warn('Continuing webhook processing despite logging exception');
  }

  try {
    switch (event.type) {

      // ---------------------------
      // PaymentIntent succeeded (LEGACY - disabled for subscription flow)
      // ---------------------------
      case 'payment_intent.succeeded': {
        console.log('⚠️ PaymentIntent event received - skipping (subscription flow handles this via invoice.paid)');
        break;
      }

      // ---------------------------
      // PaymentIntent succeeded (LEGACY HANDLER - DISABLED)
      // This case has been removed to fix TypeScript compilation
      // ---------------------------
      /*
      case 'payment_intent.succeeded_DISABLED': {
        console.log('👉 Handling payment_intent.succeeded');
        const paymentIntent = event.data.object as Stripe.PaymentIntent;

        // Extract metadata from PaymentIntent
        const {
          priceId,
          userId,
          email,
          quantity = '1',
          firstName,
          lastName,
          phone,
          country
        } = paymentIntent.metadata;

        if (!priceId || !email) {
          console.error('Missing required metadata in PaymentIntent:', paymentIntent.metadata);
          return new NextResponse('Missing required metadata', { status: 400 });
        }

        const stripeCustomerId = paymentIntent.customer as string;
        let resolvedUserId = userId;
        let fullName = firstName && lastName ? `${firstName} ${lastName}` : null;

        // Find or create Supabase auth user and profile
        if (!resolvedUserId) {
          let { data: existingProfile, error } = await supabase
            .from('profiles')
            .select('id')
            .eq('stripe_customer_id', stripeCustomerId)
            .maybeSingle();
          if (error) console.error('Error getting profile:', error);

          if (!existingProfile && email) {
            // Create Supabase auth user first
            const { data: authData, error: authError } = await supabase.auth.admin.createUser({
              email,
              email_confirm: true, // Auto-confirm email
              user_metadata: {
                full_name: fullName,
                stripe_customer_id: stripeCustomerId,
                created_via: 'payment'
              }
            });

            if (authError || !authData.user) {
              console.error('Error creating auth user:', authError);
              return new NextResponse('Could not create user account', { status: 500 });
            }

            // Create profile linked to auth user (id = auth user id)
            const { data: newProfile, error: profileInsertError } = await supabase
              .from('profiles')
              .insert({
                id: authData.user.id, // Use auth user ID as profile ID
                email,
                full_name: fullName,
                stripe_customer_id: stripeCustomerId,
              })
              .select('id')
              .single();
            if (profileInsertError) {
              console.error('Error inserting profile:', profileInsertError);
              return new NextResponse('Could not create user profile', { status: 500 });
            }
            resolvedUserId = newProfile.id;
          } else if (existingProfile) {
            resolvedUserId = existingProfile.id;
          } else {
            console.error('No email for ghost profile/no profile found');
            return new NextResponse('Cannot create profile for Stripe customer (missing email)', { status: 400 });
          }
        }

        // For PaymentIntent, we need to create a subscription record
        // First, get the price details to create a subscription-like record
        const priceDetails = await stripe.prices.retrieve(priceId);
        const productId = priceDetails.product as string;

        // Create a subscription record for the one-time payment
        const { data: subData, error: subError } = await supabase
          .from('subscriptions')
          .insert({
            user_id: resolvedUserId,
            stripe_subscription_id: null, // No subscription for one-time payments
            stripe_customer_id: stripeCustomerId,
            status: 'active',
            plan_id: priceId,
            stripe_product_id: productId,
            stripe_price_id: priceId,
            quantity: parseInt(quantity),
            current_period_start: new Date().toISOString(),
            current_period_end: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 year from now
            cancel_at_period_end: false,
          })
          .select('id')
          .single();

        if (subError || !subData?.id) {
          console.error('Error creating subscription record:', subError);
          return new NextResponse('Could not save subscription', { status: 500 });
        }
        const subscriptionId = subData.id;

        // Create license(s) for the payment
        const licenseCount = parseInt(quantity);
        const licenseRows = [];
        for (let i = 0; i < licenseCount; i++) {
          licenseRows.push({
            user_id: resolvedUserId,
            subscription_id: subscriptionId,
            license_key: generateLicenseKey(),
            product_id: productId,
            status: 'active',
            email: email, // Assign to the purchaser initially
          });
        }

        const { error: licenseInsertError } = await supabase.from('licenses').insert(licenseRows);
        if (licenseInsertError) {
          console.error('Error inserting license seats:', licenseInsertError);
          return new NextResponse('Could not create licenses', { status: 500 });
        }

        console.log('✅ PaymentIntent processed successfully');
        break;
      }
      */

      // ---------------------------
      // Initial payment + seat provisioning (Checkout Sessions)
      // ---------------------------

      case 'checkout.session.completed': {
        console.log('👉 Handling checkout.session.completed');
        const session = event.data.object as Stripe.Checkout.Session;

        let userId = session.client_reference_id ?? null;
        const stripeSubscriptionId = session.subscription as string;
        const stripeCustomerId = session.customer as string;
        let email = session.metadata?.email ?? session.customer_details?.email ?? null;
        let fullName = session.customer_details?.name || null;

        // Fallback to Stripe customer lookup if missing
        if ((!email || !fullName) && stripeCustomerId) {
          try {
            const cust = await stripe.customers.retrieve(stripeCustomerId);
            if (typeof cust === 'object') {
              if (!email && 'email' in cust && cust.email) email = cust.email;
              if (!fullName && 'name' in cust && cust.name) fullName = cust.name;
            }
          } catch (e) {
            console.error('Error fetching Stripe customer:', e);
          }
        }

        console.log(`🔍 Checkout session email data:`, {
          sessionEmail: session.metadata?.email,
          customerDetailsEmail: session.customer_details?.email,
          stripeCustomerEmail: email,
          finalEmail: email
        });

        // Reject guest emails - these indicate a configuration issue
        if (email === '<EMAIL>') {
          console.error(`❌ Detected guest email in checkout session ${session.id}. This indicates the checkout flow is not collecting real emails.`);
          console.error(`Customer ID: ${stripeCustomerId}, Subscription ID: ${stripeSubscriptionId}`);
          return new NextResponse('Invalid email - guest checkout not supported for subscriptions', { status: 400 });
        }

        // Ensure we have a valid email before proceeding
        if (!email) {
          console.error(`❌ No valid email found for checkout session ${session.id}, customer ${stripeCustomerId}`);
          return new NextResponse('No valid email found for customer', { status: 400 });
        }

        // Find or create Supabase profile
        if (!userId) {
          let { data: existingProfile, error } = await supabase
            .from('profiles')
            .select('id')
            .eq('stripe_customer_id', stripeCustomerId)
            .maybeSingle();
          if (error) console.error('Error getting profile:', error);

          if (!existingProfile && email) {
            const { data: newProfile, error: profileInsertError } = await supabase
              .from('profiles')
              .insert({
                email,
                full_name: fullName,
                stripe_customer_id: stripeCustomerId,
              })
              .select('id')
              .single();
            if (profileInsertError) {
              console.error('Error inserting profile:', profileInsertError);
              return new NextResponse('Could not create user profile', { status: 500 });
            }
            userId = newProfile.id;
          } else if (existingProfile) {
            userId = existingProfile.id;
          } else {
            console.error('No email for ghost profile/no profile found');
            return new NextResponse('Cannot create profile for Stripe customer (missing email)', { status: 400 });
          }
        }

        // Fetch full Stripe subscription
        let subscription: Stripe.Subscription;
        try {
          subscription = await stripe.subscriptions.retrieve(stripeSubscriptionId);
        } catch (e) {
          console.error('Error fetching Stripe subscription:', e);
          return new NextResponse('Could not fetch subscription', { status: 500 });
        }
        const firstItem = subscription.items.data[0];

        // Upsert subscriptions (fixed to match schema)
        const { data: subData, error: subError } = await supabase
          .from('subscriptions')
          .upsert({
            user_id: userId,
            stripe_subscription_id: subscription.id,
            status: subscription.status,
            quantity: firstItem?.quantity ?? 1,
            current_period_start: toIsoDate(firstItem?.current_period_start),
            current_period_end: toIsoDate(firstItem?.current_period_end),
            trial_start_date: toIsoDate(subscription.trial_start),
            trial_end_date: toIsoDate(subscription.trial_end),
            cancel_at_period_end: subscription.cancel_at_period_end,
          })
          .select('id')
          .single();
        if (subError || !subData?.id) {
          console.error('Error upserting subscription:', subError);
          return new NextResponse('Could not save subscription', { status: 500 });
        }
        const subscriptionId = subData.id;

        // Make sure profile has correct stripe_customer_id
        const { error: upProfile } = await supabase
          .from('profiles')
          .update({ stripe_customer_id: subscription.customer as string })
          .eq('id', userId);
        if (upProfile) console.error('Error updating profile:', upProfile);

        // Provision per-seat licenses
        const licenseCount = firstItem?.quantity ?? 1;
        // Only create if *no* licenses already exist (protects against duplicates on retry/webhook)
        const { data: existingLicenses, error: existLicError } = await supabase
          .from('licenses')
          .select('id')
          .eq('subscription_id', subscriptionId);

        if (existLicError) console.error('Error checking for licenses:', existLicError);

        if (!existingLicenses || existingLicenses.length === 0) {
          const stripeProductId = firstItem.price.product as string;
          const status =
            subscription.status === 'active' || subscription.status === 'trialing'
              ? 'active'
              : 'inactive';

          const licenseRows = [];
          for (let i = 0; i < licenseCount; i++) {
            licenseRows.push({
              user_id: userId,
              subscription_id: subscriptionId,
              license_key: generateLicenseKey(),
              product_id: stripeProductId,
              status,
              email: null, // assigned later by admin
            });
          }
          // Bulk insert for efficiency
          const { error: licenseInsertError } = await supabase.from('licenses').insert(licenseRows);
          if (licenseInsertError) {
            console.error('Error inserting license seats:', licenseInsertError);
          }
        }
        break;
      }

      // ---------------------------
      // Subscription create/update (upgrade/downgrade seats, re-activate)
      // ---------------------------
      case 'customer.subscription.created':
      case 'customer.subscription.updated': {
        console.log('👉 Handling', event.type);
        const subscription = event.data.object as Stripe.Subscription;
        const firstItem = subscription.items.data[0];

        // Declare email and fullName at case block scope for access throughout handler
        let email: string | null = null;
        let fullName: string | null = null;

        // Find/create profile
        let userId: string | null = null;
        let { data: profile, error } = await supabase
          .from('profiles')
          .select('id')
          .eq('stripe_customer_id', subscription.customer as string)
          .maybeSingle();
        if (error) console.error('Error searching for profile:', error);

        if (!profile) {
          // Profile not found by stripe_customer_id, try to find by email and update
          const stCustomer = await stripe.customers.retrieve(subscription.customer as string);
          if (typeof stCustomer === 'object' && !stCustomer.deleted) {
            if ('email' in stCustomer && stCustomer.email) email = stCustomer.email;
            if ('name' in stCustomer && stCustomer.name) fullName = stCustomer.name;
          }

          console.log(`🔍 Retrieved Stripe customer data:`, {
            customerId: subscription.customer,
            email,
            fullName,
            customerType: typeof stCustomer
          });

          // Reject guest emails - these indicate a configuration issue
          if (email === '<EMAIL>') {
            console.error(`❌ Detected guest email for customer ${subscription.customer}. This indicates the checkout flow is not collecting real emails.`);
            console.error(`Subscription ID: ${subscription.id}, Customer ID: ${subscription.customer}`);
            // Don't process this subscription - it needs manual review
            break;
          }

          if (email && email !== '<EMAIL>') {
            // Try to find existing profile by email
            const { data: existingProfile, error: findError } = await supabase
              .from('profiles')
              .select('id')
              .eq('email', email)
              .maybeSingle();

            if (findError) {
              console.error('Error finding profile by email:', findError);
            }

            console.log(`🔍 Profile lookup for ${email}:`, { existingProfile, findError });

            if (existingProfile) {
              // Update existing profile with stripe_customer_id
              const { error: updateError } = await supabase
                .from('profiles')
                .update({
                  stripe_customer_id: subscription.customer as string,
                  ...(fullName ? { full_name: fullName } : {})
                })
                .eq('id', existingProfile.id);

              if (updateError) {
                console.error('Error updating profile with stripe_customer_id:', updateError);
              } else {
                console.log('✅ Updated existing profile with stripe_customer_id');
                userId = existingProfile.id;
              }
            } else {
              // Direct purchase flow: Create auth user and profile for new customer
              console.log(`🔄 No existing profile found for ${email}. Creating new auth user and profile for direct purchase.`);

              try {
                // Create auth user using admin client
                const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
                  email,
                  email_confirm: true, // Auto-confirm email for direct purchases
                  user_metadata: {
                    full_name: fullName || '',
                    source: 'direct_purchase'
                  }
                });

                if (authError || !authUser.user) {
                  console.error('Error creating auth user:', authError);
                } else {
                  // Create profile linked to the new auth user
                  const { data: newProfile, error: profileError } = await supabase
                    .from('profiles')
                    .insert({
                      id: authUser.user.id, // Link to auth user
                      email,
                      full_name: fullName,
                      stripe_customer_id: subscription.customer as string,
                    })
                    .select('id')
                    .single();

                  if (profileError) {
                    console.error('Error creating profile for new auth user:', profileError);
                  } else {
                    console.log('✅ Created auth user and profile for direct purchase');
                    userId = newProfile.id;
                  }
                }
              } catch (error) {
                console.error('Error in direct purchase user creation:', error);
              }
            }
          }
        } else {
          userId = profile.id;
        }

        if (!userId) {
          console.error(`❌ Could not resolve user_id for subscription ${subscription.id}`);
          console.error(`Customer ID: ${subscription.customer}, Email found: ${email || 'none'}`);
          console.error('This subscription will be skipped and requires manual review');
          break;
        }

        console.log(`✅ Successfully resolved user for subscription ${subscription.id}:`, {
          userId,
          email,
          customerId: subscription.customer
        });

        // Debug: Log subscription data before processing
        console.log('🔍 Subscription data for processing:', {
          subscriptionId: subscription.id,
          status: subscription.status,
          customerId: subscription.customer,
          firstItemCurrentPeriodStart: firstItem?.current_period_start,
          firstItemCurrentPeriodEnd: firstItem?.current_period_end,
          firstItemQuantity: firstItem?.quantity,
          subscriptionCreated: subscription.created,
          subscriptionTrialStart: subscription.trial_start,
          subscriptionTrialEnd: subscription.trial_end
        });

        // Upsert subscription (fixed to match schema and use correct fields)
        const { data: subData, error: subError } = await supabase
          .from('subscriptions')
          .upsert({
            user_id: userId,
            stripe_subscription_id: subscription.id,
            status: subscription.status,
            quantity: firstItem?.quantity ?? 1,
            current_period_start: toIsoDate(firstItem?.current_period_start), // Use subscription item fields
            current_period_end: toIsoDate(firstItem?.current_period_end),     // Use subscription item fields
            trial_start: toIsoDate(subscription.trial_start),                 // Fixed: column name should be trial_start, not trial_start_date
            trial_end: toIsoDate(subscription.trial_end),                     // Fixed: column name should be trial_end, not trial_end_date
            cancel_at_period_end: subscription.cancel_at_period_end,
          })
          .select('id')
          .single();
        if (subError || !subData?.id) {
          console.error('❌ Error upserting subscription:', subError);
          console.error('Subscription data that failed:', {
            user_id: userId,
            stripe_subscription_id: subscription.id,
            status: subscription.status,
            quantity: firstItem?.quantity ?? 1,
            current_period_start: toIsoDate(firstItem?.current_period_start),
            current_period_end: toIsoDate(firstItem?.current_period_end),
            trial_start: toIsoDate(subscription.trial_start),
            trial_end: toIsoDate(subscription.trial_end),
            cancel_at_period_end: subscription.cancel_at_period_end,
          });
          break;
        }

        console.log('✅ Successfully created/updated subscription:', {
          subscriptionId: subData.id,
          stripeSubscriptionId: subscription.id,
          userId: userId
        });

        // Adjust license rows for seat count on quantity change
        const subLicenseQuery = await supabase
          .from('licenses')
          .select('id')
          .eq('subscription_id', subData.id);

        if (subLicenseQuery.error) {
          console.error('Error reading subscription licenses:', subLicenseQuery.error);
        }
        const existingCount = subLicenseQuery.data?.length ?? 0;
        const newCount = firstItem?.quantity ?? 1;

        if (existingCount < newCount) {
          // Add additional seats as needed
          const stripeProductId = firstItem.price.product as string;
          const status =
            subscription.status === 'active' || subscription.status === 'trialing'
              ? 'active'
              : 'inactive';

          console.log('🔍 Creating licenses:', {
            existingCount,
            newCount,
            licensesToCreate: newCount - existingCount,
            stripeProductId,
            status,
            subscriptionId: subData.id,
            userId
          });

          const licenseRows = [];
          for (let i = 0; i < newCount - existingCount; i++) {
            licenseRows.push({
              user_id: userId,
              subscription_id: subData.id,
              license_key: generateLicenseKey(),
              product_id: stripeProductId,
              status,
              email: null,
            });
          }
          if (licenseRows.length > 0) {
            const { error: licenseInsertError } = await supabase.from('licenses').insert(licenseRows);
            if (licenseInsertError) {
              console.error('❌ Error adding additional license seats:', licenseInsertError);
              console.error('License data that failed:', licenseRows);
            } else {
              console.log('✅ Successfully created licenses:', licenseRows.length);
            }
          }
        } else if (existingCount > newCount) {
          // Future work: Optionally handle excessive seats (deactivate or revoke extras)
          console.warn('Subscription seat count reduced: Excess licenses present (manual review may be needed).');
        }

        // Update license statuses if subscription status changed
        const statusMap: Record<string, 'active' | 'inactive' | 'canceled'> = {
          active: 'active',
          trialing: 'active',
          canceled: 'canceled',
          unpaid: 'inactive',
          past_due: 'inactive',
          incomplete: 'inactive'
        };
        const newLicenseStatus = statusMap[subscription.status] ?? 'inactive';

        const { error: licenseStatusError } = await supabase
          .from('licenses')
          .update({ status: newLicenseStatus })
          .eq('subscription_id', subData.id);

        if (licenseStatusError) {
          console.error('Error updating license status on update:', licenseStatusError);
        }

        break;
      }

      // ---------------------------
      // Subscription cancellation
      // ---------------------------
      case 'customer.subscription.deleted': {
        console.log('👉 Handling customer.subscription.deleted');
        const subscription = event.data.object as Stripe.Subscription;
        const { data: subData, error: subError } = await supabase
          .from('subscriptions')
          .update({ status: 'canceled' })
          .eq('stripe_subscription_id', subscription.id)
          .select('id')
          .single();
        if (subError) console.error('Error updating subscription as canceled:', subError);
        if (subData?.id) {
          const { error } = await supabase
            .from('licenses')
            .update({ status: 'canceled' })
            .eq('subscription_id', subData.id);
          if (error) console.error('Error canceling subscription licenses:', error);
        }
        break;
      }

      // ---------------------------
      // Invoice payment (recurring billing)
      // ---------------------------
      case 'invoice.paid': {
        console.log('👉 Handling invoice.paid');
        const invoice = event.data.object as Stripe.Invoice;

        // Skip if this is not a subscription invoice
        const invoiceWithSubscription = invoice as any;
        if (!invoiceWithSubscription.subscription) {
          console.log('Skipping non-subscription invoice');
          break;
        }

        const stripeSubscriptionId = invoiceWithSubscription.subscription as string;

        // Validate that this invoice is for our QuantBoost product
        const validPriceIds = [
          'price_1RCQeBE6FvhUKV1bUN94Oihf', // Quarterly
          'price_1RC3HTE6FvhUKV1bE9D6zf6e'  // Annual
        ];

        const invoiceLineItems = invoice.lines.data;
        const quantBoostLineItem = invoiceLineItems.find(item => {
          const itemWithPrice = item as any;
          return itemWithPrice.price && validPriceIds.includes(itemWithPrice.price.id);
        });

        if (!quantBoostLineItem) {
          console.log('Invoice does not contain QuantBoost products, skipping');
          break;
        }

        // Find the subscription in our database
        const { data: subData, error: subError } = await supabase
          .from('subscriptions')
          .select('id, user_id, status')
          .eq('stripe_subscription_id', stripeSubscriptionId)
          .single();

        if (subError || !subData) {
          console.error('Subscription not found for invoice:', stripeSubscriptionId, subError);
          break;
        }

        // Update subscription status to active and extend period
        const { error: updateError } = await supabase
          .from('subscriptions')
          .update({
            status: 'active',
            current_period_start: toIsoDate(invoice.period_start),
            current_period_end: toIsoDate(invoice.period_end),
            updated_at: new Date().toISOString()
          })
          .eq('id', subData.id);

        if (updateError) {
          console.error('Error updating subscription after payment:', updateError);
          break;
        }

        // Reactivate any licenses that might have been suspended
        const { error: licenseError } = await supabase
          .from('licenses')
          .update({
            status: 'active',
            expiry_date: toIsoDate(invoice.period_end),
            updated_at: new Date().toISOString()
          })
          .eq('subscription_id', subData.id)
          .in('status', ['past_due', 'inactive', 'expired']);

        if (licenseError) {
          console.error('Error reactivating licenses after payment:', licenseError);
        }

        console.log('✅ Invoice payment processed successfully for subscription:', stripeSubscriptionId);
        break;
      }

      // ---------------------------
      // Invoice payment failed (optional - for handling failed payments)
      // ---------------------------
      case 'invoice.payment_failed': {
        console.log('👉 Handling invoice.payment_failed');
        const invoice = event.data.object as Stripe.Invoice;

        const invoiceWithSubscription = invoice as any;
        if (!invoiceWithSubscription.subscription) {
          console.log('Skipping non-subscription invoice payment failure');
          break;
        }

        const stripeSubscriptionId = invoiceWithSubscription.subscription as string;

        // Find the subscription in our database
        const { data: subData, error: subError } = await supabase
          .from('subscriptions')
          .select('id')
          .eq('stripe_subscription_id', stripeSubscriptionId)
          .single();

        if (subError || !subData) {
          console.error('Subscription not found for failed invoice:', stripeSubscriptionId, subError);
          break;
        }

        // Update subscription status to past_due
        const { error: updateError } = await supabase
          .from('subscriptions')
          .update({
            status: 'past_due',
            updated_at: new Date().toISOString()
          })
          .eq('id', subData.id);

        if (updateError) {
          console.error('Error updating subscription after payment failure:', updateError);
          break;
        }

        // Optionally suspend licenses (or keep them active during grace period)
        // Uncomment if you want to immediately suspend licenses on payment failure
        /*
        const { error: licenseError } = await supabase
          .from('licenses')
          .update({
            status: 'past_due',
            updated_at: new Date().toISOString()
          })
          .eq('subscription_id', subData.id)
          .eq('status', 'active');

        if (licenseError) {
          console.error('Error suspending licenses after payment failure:', licenseError);
        }
        */

        console.log('✅ Invoice payment failure processed for subscription:', stripeSubscriptionId);
        break;
      }

      default:
        console.log('👀 Unhandled Stripe event type:', event.type);
        break;
    }
  } catch (err) {
    console.error('❌ Top-level webhook handler error:', err);
    return new NextResponse('Webhook handler error', { status: 500 });
  }

  return NextResponse.json({ received: true });
}

/**
 * ----------------------------
 * Admin seat assignment pattern
 * ----------------------------
 * When the admin assigns a seat in your app, call:
 * 
 * await supabase.from('licenses')
 *   .update({ email: assignedUserEmail })
 *   .eq('subscription_id', subscriptionId)
 *   .is('email', null)
 *   .limit(1);
 * 
 * Then trigger onboarding magic-link email to assigned user.
 */