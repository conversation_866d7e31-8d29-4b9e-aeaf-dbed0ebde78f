// webhooks.routes.js
// Routes for handling incoming webhooks, e.g., from Stripe

const express = require('express');
const router = express.Router();
const crypto = require('crypto');
const { supabase } = require('../supabaseClient');

// --- Stripe Initialization ---
// This block ensures that <PERSON><PERSON> is only initialized if the necessary secrets are present in the environment.
let stripe;
let endpointSecret;

if (process.env.STRIPE_SECRET_KEY && process.env.STRIPE_WEBHOOK_SIGNING_SECRET) {
    stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
    endpointSecret = process.env.STRIPE_WEBHOOK_SIGNING_SECRET;
    console.log('[Stripe Webhook] Stripe webhook endpoint is configured and enabled.');
} else {
    console.warn('[Stripe Webhook] WARNING: Stripe secrets are not configured. The /stripe webhook endpoint will be disabled.');
}

/**
 * --- Stripe Webhook Endpoint ---
 * This is the main endpoint that receives events from Strip<PERSON>.
 * It uses express.raw() middleware to get the raw request body, which is required for signature verification.
 */
router.post('/stripe', express.raw({type: 'application/json'}), async (req, res) => {
    // Immediately halt if Stripe is not configured.
    if (!stripe || !endpointSecret) {
        console.error("[Stripe Webhook] Received a webhook but secrets are not configured. Aborting.");
        // Use 503 Service Unavailable to indicate a temporary server-side misconfiguration.
        return res.status(503).send("Service is not configured to handle Stripe webhooks.");
    }

    const sig = req.headers['stripe-signature'];
    let event;

    // Step 1: Verify the webhook signature to ensure the request is from Stripe.
    try {
        event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
        console.log(`[Stripe Webhook] Event ${event.id} received. Type: ${event.type}`);
    } catch (err) {
        console.error(`[Stripe Webhook] SIGNATURE VERIFICATION FAILED: ${err.message}`);
        return res.status(400).send(`Webhook Error: Signature verification failed.`);
    }

    // Step 2: Extract relevant data and dispatch the event to the handler.
    let subscription;
    let customerId;
    let userEmail;

    try {
        switch (event.type) {
            case 'checkout.session.completed':
                const session = event.data.object;
                // This is the most common event for new subscriptions.
                if (session.mode === 'subscription' && session.subscription && session.payment_status === 'paid') {
                    console.log(`[Stripe Webhook] Processing 'checkout.session.completed' for subscription: ${session.subscription}`);
                    subscription = await stripe.subscriptions.retrieve(session.subscription);
                    customerId = subscription.customer;
                    const customer = await stripe.customers.retrieve(customerId);
                    userEmail = customer.email;
                    await handleSubscriptionUpdate(subscription, userEmail, customerId, event.type);
                }
                break;

            case 'customer.subscription.created':
            case 'customer.subscription.updated':
                subscription = event.data.object;
                customerId = subscription.customer;
                const customerForUpdate = await stripe.customers.retrieve(customerId);
                userEmail = customerForUpdate.email;
                console.log(`[Stripe Webhook] Processing '${event.type}' for subscription: ${subscription.id}`);
                await handleSubscriptionUpdate(subscription, userEmail, customerId, event.type);
                break;

            case 'invoice.paid':
                const invoice = event.data.object;
                // This event is useful for confirming renewals.
                if (invoice.paid && invoice.subscription) {
                    console.log(`[Stripe Webhook] Processing 'invoice.paid' for subscription: ${invoice.subscription}`);
                    subscription = await stripe.subscriptions.retrieve(invoice.subscription);
                    customerId = subscription.customer;
                    const customerForInvoice = await stripe.customers.retrieve(customerId);
                    userEmail = customerForInvoice.email;
                    await handleSubscriptionUpdate(subscription, userEmail, customerId, event.type);
                }
                break;

            default:
                console.log(`[Stripe Webhook] Unhandled event type: ${event.type}`);
        }
    } catch (err) {
        console.error(`[Stripe Webhook] Error during event processing for event ${event.id}: ${err.message}`, err.stack);
        // Return a 500 error to tell Stripe to retry the webhook.
        return res.status(500).json({ error: 'An internal error occurred while processing the webhook.' });
    }

    // Step 3: Acknowledge receipt of the event with a 200 status code.
    res.status(200).json({ received: true });
});

/**
 * --- Main Business Logic Handler ---
 * This function contains the core logic for creating/updating users, subscriptions, and licenses in your database.
 * It is designed to be idempotent and robust.
 */
async function handleSubscriptionUpdate(stripeSubscription, userEmail, stripeCustomerId, eventType) {
    console.log(`[Handler] Processing: StripeSubID=${stripeSubscription.id}, Email=${userEmail}, StripeCustID=${stripeCustomerId}, Event=${eventType}`);

    // --- Data Validation and Enrichment ---
    // Ensure we have an email, which is the primary key for finding users.
    if (!userEmail) {
        console.error(`[Handler] CRITICAL: Cannot process subscription ${stripeSubscription.id} without a user email. Aborting.`);
        return; // Stop processing if no email is available.
    }

    try {
        // --- Profile and User Management ---
        // This is the central part of the fix.

        // Check if a profile already exists for this user.
        const { data: existingProfile, error: profileFetchError } = await supabase
            .from('profiles')
            .select('id, email, stripe_customer_id')
            .eq('email', userEmail)
            .maybeSingle();

        if (profileFetchError) {
            console.error(`[Handler] DB Error: Failed to fetch profile for ${userEmail}. Error: ${profileFetchError.message}`);
            throw new Error('Database query for profile failed.'); // Throw to trigger retry from Stripe.
        }

        let profile = existingProfile;

        // --- NEW USER LOGIC ---
        // If no profile exists, this is a new customer from Stripe Checkout.
        if (!profile) {
            console.log(`[Handler] Profile not found for ${userEmail}. This is a new user. Creating auth record...`);

            // Step 1: Create the user in Supabase Auth. This is the action that was causing the race condition.
            const { data: newUser, error: createUserError } = await supabase.auth.admin.createUser({
                email: userEmail,
                email_confirm: true, // Auto-confirm the email since the user has paid.
            });

            if (createUserError) {
                console.error(`[Handler] CRITICAL: Failed to create auth user for ${userEmail}. Error: ${createUserError.message}`);
                throw new Error('Failed to create new auth user.');
            }

            console.log(`[Handler] Auth user created successfully. ID: ${newUser.user.id}`);

            // Step 2: THE FIX. A trigger on `auth.users` has already created a `profiles` row.
            // We now UPDATE that row with the Stripe Customer ID. This avoids the 409 Conflict.
            const { data: updatedProfile, error: updateProfileError } = await supabase
                .from('profiles')
                .update({ stripe_customer_id: stripeCustomerId })
                .eq('id', newUser.user.id) // The profile ID matches the new auth user ID.
                .select()
                .single();

            if (updateProfileError) {
                console.error(`[Handler] DB Error: Failed to update new profile for user ${newUser.user.id} with Stripe ID. Error: ${updateProfileError.message}`);
                throw new Error('Failed to update new profile with Stripe ID.');
            }

            console.log(`[Handler] Profile for new user ${newUser.user.id} successfully updated with Stripe Customer ID.`);
            profile = updatedProfile; // Assign the newly created and updated profile to our working variable.
        }
        // --- EXISTING USER LOGIC ---
        else if (profile.stripe_customer_id !== stripeCustomerId) {
            // If the profile exists but the Stripe ID is missing or different, update it.
            console.log(`[Handler] Existing profile found for ${userEmail}. Updating Stripe Customer ID.`);
            const { error: updateStripeIdError } = await supabase
                .from('profiles')
                .update({ stripe_customer_id: stripeCustomerId, updated_at: new Date().toISOString() })
                .eq('id', profile.id);

            if (updateStripeIdError) {
                console.warn(`[Handler] DB Warning: Failed to update stripe_customer_id for profile ${profile.id}. Error: ${updateStripeIdError.message}`);
                // This is a warning, not a critical failure. The process can continue.
            } else {
                profile.stripe_customer_id = stripeCustomerId; // Update in memory for the rest of the function.
            }
        }

        const userId = profile.id;

        // --- Subscription and License Database Updates ---
        // With a valid `profile` and `userId`, we can now safely upsert subscription and license info.

        // Upsert Subscription Record
        const subscriptionDataForDB = {
            id: stripeSubscription.id, // Use Stripe's ID as the primary key for idempotency.
            user_id: userId,
            status: mapStripeSubscriptionStatus(stripeSubscription.status),
            quantity: stripeSubscription.items.data[0]?.quantity || 1,
            current_period_start: new Date(stripeSubscription.current_period_start * 1000).toISOString(),
            current_period_end: new Date(stripeSubscription.current_period_end * 1000).toISOString(),
            trial_start_date: stripeSubscription.trial_start ? new Date(stripeSubscription.trial_start * 1000).toISOString() : null,
            trial_end_date: stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000).toISOString() : null,
            cancel_at_period_end: stripeSubscription.cancel_at_period_end,
            canceled_at: stripeSubscription.canceled_at ? new Date(stripeSubscription.canceled_at * 1000).toISOString() : null,
            updated_at: new Date().toISOString(),
        };

        const { error: upsertSubError } = await supabase.from('subscriptions').upsert(subscriptionDataForDB, { onConflict: 'id' });

        if (upsertSubError) {
            console.error(`[Handler] DB Error: Failed to upsert subscription ${stripeSubscription.id} for user ${userId}. Error: ${upsertSubError.message}`);
            throw new Error('Failed to upsert subscription.');
        }
        console.log(`[Handler] Subscription ${stripeSubscription.id} for user ${userId} successfully upserted.`);

        // Upsert License Record
        const productId = getProductIdFromStripeSubscription(stripeSubscription);
        const { data: existingLicense, error: licenseFetchErr } = await supabase
            .from('licenses')
            .select('id')
            .eq('subscription_id', stripeSubscription.id)
            .maybeSingle();

        if (licenseFetchErr) {
            console.error(`[Handler] DB Error: Failed to check for existing license for sub ${stripeSubscription.id}. Error: ${licenseFetchErr.message}`);
            throw new Error('Failed to query for license.');
        }

        const licenseData = {
            user_id: userId,
            email: userEmail,
            product_id: productId,
            subscription_id: stripeSubscription.id,
            status: mapStripeSubscriptionStatusToLicenseStatus(stripeSubscription.status, stripeSubscription.trial_end),
            expiry_date: new Date(stripeSubscription.current_period_end * 1000).toISOString(),
            license_tier: getLicenseTierFromStripeSubscription(stripeSubscription),
            max_activations: getMaxActivationsFromStripeSubscription(stripeSubscription),
            updated_at: new Date().toISOString(),
        };

        if (existingLicense) {
            // Update existing license
            const { error: updateLicError } = await supabase.from('licenses').update(licenseData).eq('id', existingLicense.id);
            if (updateLicError) {
                console.error(`[Handler] DB Error: Failed to update license ${existingLicense.id}. Error: ${updateLicError.message}`);
                throw new Error('Failed to update license.');
            }
            console.log(`[Handler] License ${existingLicense.id} successfully updated.`);
        } else {
            // Create new license
            const newLicenseData = {
                ...licenseData,
                license_key: `${productId.toUpperCase()}-${crypto.randomBytes(6).toString('hex').toUpperCase()}`,
            };
            const { error: insertLicError } = await supabase.from('licenses').insert(newLicenseData);
            if (insertLicError) {
                console.error(`[Handler] DB Error: Failed to create new license for user ${userId}. Error: ${insertLicError.message}`);
                throw new Error('Failed to create new license.');
            }
            console.log(`[Handler] New license for user ${userId} successfully created.`);
        }

    } catch (error) {
        console.error(`[Handler] UNEXPECTED ERROR processing subscription ${stripeSubscription.id} for ${userEmail}: ${error.message}`, error.stack);
        // Re-throw the error to ensure the calling context knows to send a 500 response,
        // which will signal Stripe to retry the webhook.
        throw error;
    }
}


// --- Helper Functions for Mapping Data ---
// These functions translate Stripe's data format to your application's schema.

function mapStripeSubscriptionStatus(stripeStatus) {
    const statusMap = {
        'trialing': 'trialing',
        'active': 'active',
        'past_due': 'past_due',
        'canceled': 'canceled',
        'unpaid': 'unpaid',
        'incomplete': 'incomplete',
        'incomplete_expired': 'incomplete_expired',
    };
    return statusMap[stripeStatus] || 'inactive';
}

function mapStripeSubscriptionStatusToLicenseStatus(stripeStatus, stripeTrialEndTimestamp) {
    const now = new Date();
    const trialEndDate = stripeTrialEndTimestamp ? new Date(stripeTrialEndTimestamp * 1000) : null;

    switch (stripeStatus) {
        case 'trialing':
            return (trialEndDate && trialEndDate > now) ? 'trial_active' : 'expired';
        case 'active':
            return 'active';
        case 'past_due':
            return 'grace_period'; // User has a short time to fix payment.
        case 'canceled':
        case 'unpaid':
        case 'incomplete_expired':
            return 'canceled';
        default:
            return 'inactive';
    }
}

function getProductIdFromStripeSubscription(stripeSubscription) {
    return stripeSubscription.metadata?.app_product_id || process.env.DEFAULT_PRODUCT_ID || 'default-product';
}

function getLicenseTierFromStripeSubscription(stripeSubscription) {
    if (stripeSubscription.metadata?.app_license_tier) {
        return stripeSubscription.metadata.app_license_tier;
    }
    const priceNickname = stripeSubscription.items.data[0]?.price?.nickname?.toLowerCase() || '';
    if (priceNickname.includes('pro')) return 'Pro';
    if (priceNickname.includes('enterprise')) return 'Enterprise';
    return process.env.DEFAULT_LICENSE_TIER || 'Standard';
}

function getMaxActivationsFromStripeSubscription(stripeSubscription) {
    if (stripeSubscription.metadata?.app_max_activations) {
        const maxActivations = parseInt(stripeSubscription.metadata.app_max_activations, 10);
        if (!isNaN(maxActivations)) return maxActivations;
    }
    const tier = getLicenseTierFromStripeSubscription(stripeSubscription);
    if (tier === 'Enterprise') return 5;
    if (tier === 'Pro') return 2;
    return parseInt(process.env.DEFAULT_MAX_ACTIVATIONS || '1', 10);
}

module.exports = router;