// Test script to verify local Stripe webhook endpoint functionality
const crypto = require('crypto');

// Test local webhook endpoint
async function testLocalWebhookEndpoint() {
  const webhookSecret = 'whsec_7f9a4f74382639cea4d3dd0844e14acb61c4534165edcc6bc21a051f5537ce0f';
  const endpointUrl = 'http://localhost:3000/api/webhooks/stripe';
  
  // Create a test subscription created event
  const testEvent = {
    id: 'evt_test_local_' + Date.now(),
    object: 'event',
    type: 'customer.subscription.created',
    created: Math.floor(Date.now() / 1000),
    data: {
      object: {
        id: 'sub_test_local_' + Date.now(),
        object: 'subscription',
        customer: 'cus_test_local_customer',
        status: 'active',
        items: {
          data: [{
            price: {
              id: 'price_1RCQeBE6FvhUKV1bUN94Oihf'  // Quarterly price
            },
            quantity: 2
          }]
        },
        current_period_start: Math.floor(Date.now() / 1000),
        current_period_end: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60),
        created: Math.floor(Date.now() / 1000),
        cancel_at_period_end: false,
        canceled_at: null,
        trial_start: null,
        trial_end: null
      }
    }
  };

  const payload = JSON.stringify(testEvent);
  const timestamp = Math.floor(Date.now() / 1000);
  
  // Create Stripe signature
  const signedPayload = timestamp + '.' + payload;
  const signature = crypto
    .createHmac('sha256', webhookSecret)
    .update(signedPayload, 'utf8')
    .digest('hex');
  
  const stripeSignature = `t=${timestamp},v1=${signature}`;

  console.log('🧪 Testing LOCAL webhook endpoint...');
  console.log('📍 Endpoint:', endpointUrl);
  console.log('📦 Event ID:', testEvent.id);
  console.log('📦 Event Type:', testEvent.type);
  console.log('🔐 Signature:', stripeSignature);

  try {
    const response = await fetch(endpointUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'stripe-signature': stripeSignature
      },
      body: payload
    });

    console.log('📊 Response Status:', response.status);
    console.log('📋 Response Headers:', Object.fromEntries(response.headers.entries()));
    
    const responseText = await response.text();
    console.log('📄 Response Body:', responseText);

    if (response.ok) {
      console.log('✅ Local webhook endpoint test PASSED');
      return true;
    } else {
      console.log('❌ Local webhook endpoint test FAILED');
      return false;
    }
  } catch (error) {
    console.error('💥 Error testing local webhook:', error);
    return false;
  }
}

// Test checkout session completed event
async function testCheckoutSessionCompleted() {
  const webhookSecret = 'whsec_7f9a4f74382639cea4d3dd0844e14acb61c4534165edcc6bc21a051f5537ce0f';
  const endpointUrl = 'http://localhost:3000/api/webhooks/stripe';
  
  const testEvent = {
    id: 'evt_checkout_test_' + Date.now(),
    object: 'event',
    type: 'checkout.session.completed',
    created: Math.floor(Date.now() / 1000),
    data: {
      object: {
        id: 'cs_test_' + Date.now(),
        object: 'checkout.session',
        mode: 'subscription',
        customer: 'cus_test_checkout_customer',
        customer_details: {
          email: '<EMAIL>'
        },
        subscription: 'sub_test_from_checkout'
      }
    }
  };

  const payload = JSON.stringify(testEvent);
  const timestamp = Math.floor(Date.now() / 1000);
  
  const signedPayload = timestamp + '.' + payload;
  const signature = crypto
    .createHmac('sha256', webhookSecret)
    .update(signedPayload, 'utf8')
    .digest('hex');
  
  const stripeSignature = `t=${timestamp},v1=${signature}`;

  console.log('\n🧪 Testing checkout.session.completed event...');
  console.log('📦 Event ID:', testEvent.id);
  console.log('📧 Customer Email:', testEvent.data.object.customer_details.email);

  try {
    const response = await fetch(endpointUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'stripe-signature': stripeSignature
      },
      body: payload
    });

    console.log('📊 Response Status:', response.status);
    const responseText = await response.text();
    console.log('📄 Response Body:', responseText);

    if (response.ok) {
      console.log('✅ Checkout session test PASSED');
      return true;
    } else {
      console.log('❌ Checkout session test FAILED');
      return false;
    }
  } catch (error) {
    console.error('💥 Error testing checkout session:', error);
    return false;
  }
}

// Run local tests
async function runLocalTests() {
  console.log('🚀 Starting LOCAL webhook endpoint tests...\n');
  
  const test1 = await testLocalWebhookEndpoint();
  const test2 = await testCheckoutSessionCompleted();
  
  console.log('\n📊 Local Test Results:');
  console.log('✅ Subscription created test:', test1 ? 'PASSED' : 'FAILED');
  console.log('✅ Checkout session test:', test2 ? 'PASSED' : 'FAILED');
  
  if (test1 && test2) {
    console.log('\n🎉 All LOCAL webhook tests PASSED! Ready for production deployment.');
  } else {
    console.log('\n❌ Some LOCAL tests FAILED. Check the logs above for details.');
  }
}

runLocalTests();
