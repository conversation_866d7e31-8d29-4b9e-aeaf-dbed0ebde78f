name: Deploy Frontend to Azure Static Web Apps (Staging)

on:
  push:
    branches: [ main, staging ]
    paths: 
      - 'QuantBoost_Frontend/**'
  pull_request:
    types: [opened, synchronize, reopened, closed]
    branches: [ main ]
    paths:
      - 'QuantBoost_Frontend/**'
  workflow_dispatch:  # Allow manual deployment

env:
  NODE_VERSION: '18'
  AZURE_STATIC_WEB_APPS_API_TOKEN: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN_STAGING }}

jobs:
  build_and_deploy_job:
    if: github.event_name == 'push' || github.event_name == 'workflow_dispatch' || (github.event_name == 'pull_request' && github.event.action != 'closed')
    runs-on: ubuntu-latest
    name: Build and Deploy Job
    steps:
      - uses: actions/checkout@v4
        with:
          submodules: true
          lfs: false

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          cache-dependency-path: QuantBoost_Frontend/package-lock.json

      - name: Install dependencies
        run: |
          cd QuantBoost_Frontend
          npm ci

      - name: Build application
        run: |
          cd QuantBoost_Frontend
          npm run build
        env:
          # Staging environment variables
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY }}
          NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: ${{ secrets.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY_TEST }}
          NEXT_PUBLIC_BASE_URL: https://purple-glacier-0ab50190f.1.azurestaticapps.net
          NEXT_PUBLIC_AZURE_API_URL: https://ca-quantboost-api.proudwater-12345678.westus3.azurecontainerapps.io

      - name: Deploy to Azure Static Web Apps
        id: builddeploy
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ env.AZURE_STATIC_WEB_APPS_API_TOKEN }}
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          action: "upload"
          app_location: "QuantBoost_Frontend"
          api_location: "QuantBoost_Frontend/api"
          output_location: "out"
          skip_app_build: true  # We already built it
          api_build_command: "npm install"

  close_pull_request_job:
    if: github.event_name == 'pull_request' && github.event.action == 'closed'
    runs-on: ubuntu-latest
    name: Close Pull Request Job
    steps:
      - name: Close Pull Request
        id: closepullrequest
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ env.AZURE_STATIC_WEB_APPS_API_TOKEN }}
          action: "close"
