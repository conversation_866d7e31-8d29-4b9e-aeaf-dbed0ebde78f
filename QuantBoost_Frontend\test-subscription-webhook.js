const crypto = require('crypto');

// Test webhook for subscription creation
async function testSubscriptionWebhook() {
  const webhookUrl = 'http://localhost:3000/api/webhooks/stripe';
  
  // Create a realistic test subscription event
  const testEvent = {
    id: 'evt_test_subscription_' + Date.now(),
    object: 'event',
    type: 'customer.subscription.created',
    created: Math.floor(Date.now() / 1000),
    data: {
      object: {
        id: 'sub_test_' + Date.now(),
        object: 'subscription',
        customer: 'cus_SkPkZlHKKdP0Vl', // Use the existing customer ID from the test
        status: 'active',
        items: {
          data: [{
            id: 'si_test_' + Date.now(),
            object: 'subscription_item',
            price: {
              id: 'price_1RC3HTE6FvhUKV1bE9D6zf6e', // Annual plan price ID
              product: 'prod_1RC3HTE6FvhUKV1bE9D6zf6e'
            },
            quantity: 1,
            current_period_start: Math.floor(Date.now() / 1000),
            current_period_end: Math.floor(Date.now() / 1000) + (365 * 24 * 60 * 60) // 1 year
          }]
        },
        created: Math.floor(Date.now() / 1000),
        cancel_at_period_end: false,
        canceled_at: null,
        trial_start: null,
        trial_end: null
      }
    }
  };

  const payload = JSON.stringify(testEvent);
  
  // Create a test signature (this won't be valid but will help test the flow)
  const signature = crypto
    .createHmac('sha256', 'test_webhook_secret')
    .update(payload)
    .digest('hex');

  try {
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Stripe-Signature': `t=${Math.floor(Date.now() / 1000)},v1=${signature}`,
      },
      body: payload,
    });

    console.log('Webhook Response Status:', response.status);
    const responseText = await response.text();
    console.log('Webhook Response:', responseText);

    if (response.ok) {
      console.log('✅ Webhook test successful!');
    } else {
      console.log('❌ Webhook test failed');
    }
  } catch (error) {
    console.error('Error testing webhook:', error);
  }
}

// Run the test
testSubscriptionWebhook();
