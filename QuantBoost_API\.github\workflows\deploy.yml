name: Build and Deploy to Azure

on:
  push:
    branches: [ main ]
    paths:
    - 'QuantBoost_API/**'
    - '.github/workflows/deploy.yml'
  workflow_dispatch:  # Enables manual trigger
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'production'
        type: choice
        options:
        - production

env:
  CONTAINER_NAME: ca-quantboost-api
  RESOURCE_GROUP: rg-quantboost-api-prod
  CONTAINER_APP_NAME: ca-quantboost-api
  REGISTRY_URL: acrquantboostup46p0.azurecr.io

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Use Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'

    - name: Install dependencies
      run: |
        cd QuantBoost_API
        npm ci

    - name: Run tests
      run: |
        cd QuantBoost_API
        npm test

    - name: Run linting
      run: |
        cd QuantBoost_API
        npm run lint

  build-and-deploy:
    needs: test
    runs-on: ubuntu-latest
    permissions:
      id-token: write #This is required for requesting the OIDC JWT Token
      contents: read #Required when GH token is used to authenticate with private repo
    if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'

    steps:
    - name: Checkout to the branch
      uses: actions/checkout@v4

    - name: Azure Login
      uses: azure/login@v1
      with:
        client-id: ${{ secrets.CAQUANTBOOSTAPI_AZURE_CLIENT_ID }}
        tenant-id: ${{ secrets.CAQUANTBOOSTAPI_AZURE_TENANT_ID }}
        subscription-id: ${{ secrets.CAQUANTBOOSTAPI_AZURE_SUBSCRIPTION_ID }}

    - name: Build and push container image to registry
      uses: azure/container-apps-deploy-action@v1
      with:
        appSourcePath: ${{ github.workspace }}/QuantBoost_API
        registryUrl: ${{ env.REGISTRY_URL }}
        registryUsername: ${{ secrets.CAQUANTBOOSTAPI_REGISTRY_USERNAME }}
        registryPassword: ${{ secrets.CAQUANTBOOSTAPI_REGISTRY_PASSWORD }}
        containerAppName: ${{ env.CONTAINER_APP_NAME }}
        resourceGroup: ${{ env.RESOURCE_GROUP }}
        imageToBuild: ${{ env.REGISTRY_URL }}/${{ env.CONTAINER_NAME }}:${{ github.sha }}

    - name: Deployment Summary
      run: |
        echo "🚀 Deployment completed successfully!"
        echo "📦 Image: ${{ env.REGISTRY_URL }}/${{ env.CONTAINER_NAME }}:${{ github.sha }}"
        echo "🏗️ Container App: ${{ env.CONTAINER_APP_NAME }}"
        echo "📍 Resource Group: ${{ env.RESOURCE_GROUP }}"
        echo "🔗 Check deployment status in the Azure portal."