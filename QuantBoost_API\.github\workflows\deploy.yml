name: Build and Deploy to Azure

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:  # Enables manual trigger
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        default: 'production'
        type: choice
        options:
        - production

env:
  CONTAINER_NAME: quantboost-api
  RESOURCE_GROUP: rg-quantboost-api-prod
  CONTAINER_APP_NAME: ca-quantboost-api

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run tests
      run: npm test
      
    - name: Run linting
      run: npm run lint

  build-and-deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.event_name == 'workflow_dispatch'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Azure Login
      uses: azure/login@v1
      with:
        creds: |
          {
            "clientId": "${{ secrets.AZURE_CLIENT_ID }}",
            "clientSecret": "${{ secrets.AZURE_CLIENT_SECRET }}",
            "subscriptionId": "${{ secrets.AZURE_SUBSCRIPTION_ID }}",
            "tenantId": "${{ secrets.AZURE_TENANT_ID }}"
          }
    
    - name: Setup Terraform
      uses: hashicorp/setup-terraform@v3
      with:
        terraform_version: "1.5.0"
    
    - name: Get ACR name from Terraform outputs
      id: get-acr
      run: |
        cd infrastructure
        terraform init
        ACR_NAME=$(terraform output -raw container_registry_name)
        echo "acr_name=${ACR_NAME}" >> $GITHUB_OUTPUT
        echo "Using ACR: ${ACR_NAME}"
    
    - name: Login to Azure Container Registry
      run: |
        az acr login --name ${{ steps.get-acr.outputs.acr_name }}
    
    - name: Build and push image
      run: |
        ACR_NAME=${{ steps.get-acr.outputs.acr_name }}
        TIMESTAMP=$(date +%Y%m%d-%H%M%S)
        IMAGE_TAG="${{ github.sha }}-${TIMESTAMP}"

        echo "Building image with tag: ${IMAGE_TAG}"
        docker build . -t ${ACR_NAME}.azurecr.io/${{ env.CONTAINER_NAME }}:${IMAGE_TAG}
        docker tag ${ACR_NAME}.azurecr.io/${{ env.CONTAINER_NAME }}:${IMAGE_TAG} ${ACR_NAME}.azurecr.io/${{ env.CONTAINER_NAME }}:latest

        echo "Pushing images to registry..."
        docker push ${ACR_NAME}.azurecr.io/${{ env.CONTAINER_NAME }}:${IMAGE_TAG}
        docker push ${ACR_NAME}.azurecr.io/${{ env.CONTAINER_NAME }}:latest

        echo "IMAGE_TAG=${IMAGE_TAG}" >> $GITHUB_OUTPUT
      id: build
    
    - name: Deploy to Container Apps
      run: |
        ACR_NAME=${{ steps.get-acr.outputs.acr_name }}
        IMAGE_TAG=${{ steps.build.outputs.IMAGE_TAG }}
        NEW_IMAGE="${ACR_NAME}.azurecr.io/${{ env.CONTAINER_NAME }}:${IMAGE_TAG}"

        echo "Deploying image: ${NEW_IMAGE}"
        echo "Container App: ${{ env.CONTAINER_APP_NAME }}"
        echo "Resource Group: ${{ env.RESOURCE_GROUP }}"

        az containerapp update \
          --name ${{ env.CONTAINER_APP_NAME }} \
          --resource-group ${{ env.RESOURCE_GROUP }} \
          --image ${NEW_IMAGE}

        echo "Deployment completed successfully!"
        echo "You can check the deployment status in the Azure portal."