name: Build and Deploy to Azure

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  CONTAINER_NAME: quantboost-api
  RESOURCE_GROUP: rg-quantboost-api-prod
  CONTAINER_APP_NAME: ca-quantboost-api

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run tests
      run: npm test
      
    - name: Run linting
      run: npm run lint

  build-and-deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Setup Azure CLI
      run: |
        curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
        
    - name: Login to Azure with Service Principal
      run: |
        az login --service-principal \
          -u ${{ secrets.AZURE_CLIENT_ID }} \
          -p ${{ secrets.AZURE_CLIENT_SECRET }} \
          --tenant ${{ secrets.AZURE_TENANT_ID }}
    
    - name: Setup Terraform
      uses: hashicorp/setup-terraform@v3
      with:
        terraform_version: "1.5.0"
    
    - name: Get ACR name from Terraform outputs
      id: get-acr
      run: |
        cd infrastructure
        terraform init
        ACR_NAME=$(terraform output -raw container_registry_name)
        echo "acr_name=${ACR_NAME}" >> $GITHUB_OUTPUT
        echo "Using ACR: ${ACR_NAME}"
    
    - name: Login to Azure Container Registry
      run: |
        az acr login --name ${{ steps.get-acr.outputs.acr_name }}
    
    - name: Build and push image
      run: |
        ACR_NAME=${{ steps.get-acr.outputs.acr_name }}
        docker build . -t ${ACR_NAME}.azurecr.io/${{ env.CONTAINER_NAME }}:${{ github.sha }}
        docker tag ${ACR_NAME}.azurecr.io/${{ env.CONTAINER_NAME }}:${{ github.sha }} ${ACR_NAME}.azurecr.io/${{ env.CONTAINER_NAME }}:latest
        docker push ${ACR_NAME}.azurecr.io/${{ env.CONTAINER_NAME }}:${{ github.sha }}
        docker push ${ACR_NAME}.azurecr.io/${{ env.CONTAINER_NAME }}:latest
    
    - name: Deploy to Container Apps
      run: |
        ACR_NAME=${{ steps.get-acr.outputs.acr_name }}
        az containerapp update \
          --name ${{ env.CONTAINER_APP_NAME }} \
          --resource-group ${{ env.RESOURCE_GROUP }} \
          --image ${ACR_NAME}.azurecr.io/${{ env.CONTAINER_NAME }}:${{ github.sha }}