'use client';

import { useState, useEffect, useRef, memo, useMemo, useCallback } from 'react';

import { loadStripe } from '@stripe/stripe-js';
import {
  Elements,
  PaymentElement,
  LinkAuthenticationElement,
  useStripe,
  useElements
} from '@stripe/react-stripe-js';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Button,
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  Input,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Checkbox,
  FormField,
  FormFieldLabel,
  FormFieldError,
  FormFieldDescription
} from '@/components/ui';
import { Shield, Lock, CreditCard, User, Users, MapPin } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
import { useSupabaseClient } from '../../../hooks/useSupabaseClient';
import { useRouter, useParams, useSearchParams } from 'next/navigation';
import {
  checkoutFormWithoutEmailSchema,
  type CheckoutFormWithoutEmailData,
  COUNTRIES,
  getStatesForCountry,
  validatePostalCode
} from '@/lib/validation';

// Initialize Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

// Product details - Individual and Team plans use same price IDs
const PRODUCTS = {
  'price_1RC3HTE6FvhUKV1bE9D6zf6e': {
    name: 'Individual Annual',
    price: '$120',
    period: 'per year',
    description: 'Full access for one user, billed annually.',
    features: ['All Excel Modules', 'All PowerPoint Modules', 'Standard Support'],
  },
  'price_1RCQeBE6FvhUKV1bUN94Oihf': {
    name: 'Individual Quarterly',
    price: '$45',
    period: 'per quarter',
    description: 'Full access for one user, billed quarterly.',
    features: ['All Excel Modules', 'All PowerPoint Modules', 'Standard Support'],
  },
};

// Team plans use the same price IDs but with quantity > 1
// We'll determine if it's a team plan based on the URL parameter ?quantity=X where X > 1
const TEAM_PRICE_IDS = [
  'price_1RC3HTE6FvhUKV1bE9D6zf6e', // Annual plan used for teams
  'price_1RCQeBE6FvhUKV1bUN94Oihf', // Quarterly plan used for teams
];

// US States list for dropdown
const US_STATES = [
  { value: 'AL', label: 'Alabama' },
  { value: 'AK', label: 'Alaska' },
  { value: 'AZ', label: 'Arizona' },
  { value: 'AR', label: 'Arkansas' },
  { value: 'CA', label: 'California' },
  { value: 'CO', label: 'Colorado' },
  { value: 'CT', label: 'Connecticut' },
  { value: 'DE', label: 'Delaware' },
  { value: 'FL', label: 'Florida' },
  { value: 'GA', label: 'Georgia' },
  { value: 'HI', label: 'Hawaii' },
  { value: 'ID', label: 'Idaho' },
  { value: 'IL', label: 'Illinois' },
  { value: 'IN', label: 'Indiana' },
  { value: 'IA', label: 'Iowa' },
  { value: 'KS', label: 'Kansas' },
  { value: 'KY', label: 'Kentucky' },
  { value: 'LA', label: 'Louisiana' },
  { value: 'ME', label: 'Maine' },
  { value: 'MD', label: 'Maryland' },
  { value: 'MA', label: 'Massachusetts' },
  { value: 'MI', label: 'Michigan' },
  { value: 'MN', label: 'Minnesota' },
  { value: 'MS', label: 'Mississippi' },
  { value: 'MO', label: 'Missouri' },
  { value: 'MT', label: 'Montana' },
  { value: 'NE', label: 'Nebraska' },
  { value: 'NV', label: 'Nevada' },
  { value: 'NH', label: 'New Hampshire' },
  { value: 'NJ', label: 'New Jersey' },
  { value: 'NM', label: 'New Mexico' },
  { value: 'NY', label: 'New York' },
  { value: 'NC', label: 'North Carolina' },
  { value: 'ND', label: 'North Dakota' },
  { value: 'OH', label: 'Ohio' },
  { value: 'OK', label: 'Oklahoma' },
  { value: 'OR', label: 'Oregon' },
  { value: 'PA', label: 'Pennsylvania' },
  { value: 'RI', label: 'Rhode Island' },
  { value: 'SC', label: 'South Carolina' },
  { value: 'SD', label: 'South Dakota' },
  { value: 'TN', label: 'Tennessee' },
  { value: 'TX', label: 'Texas' },
  { value: 'UT', label: 'Utah' },
  { value: 'VT', label: 'Vermont' },
  { value: 'VA', label: 'Virginia' },
  { value: 'WA', label: 'Washington' },
  { value: 'WV', label: 'West Virginia' },
  { value: 'WI', label: 'Wisconsin' },
  { value: 'WY', label: 'Wyoming' },
];



// Isolated PaymentElement component to prevent re-renders from parent state changes
const IsolatedPaymentElement = memo(function IsolatedPaymentElement({
  formData
}: {
  formData: any
}) {
  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
        <CreditCard className="h-4 w-4" />
        Payment Information
      </div>
      <div className="p-4 border rounded-lg bg-gray-50/50">
        <PaymentElement
          options={{
            business: { name: "QuantBoost" },
            defaultValues: {
              billingDetails: {
                name: formData.firstName && formData.lastName
                  ? `${formData.firstName} ${formData.lastName}`.trim()
                  : '',
                email: formData.email || '',
                address: {
                  line1: formData.addressLine1 || '',
                  line2: formData.addressLine2 || '',
                  city: formData.city || '',
                  state: formData.state || '',
                  postal_code: formData.postalCode || '',
                  country: formData.country || 'US',
                },
              },
            },
            fields: {
              billingDetails: {
                name: 'auto',
                email: 'auto',
                address: {
                  country: 'auto',
                  line1: 'auto',
                  line2: 'auto',
                  city: 'auto',
                  state: 'auto',
                  postalCode: 'auto',
                },
              },
            },
            layout: {
              type: 'tabs',
              defaultCollapsed: false,
            },
          }}
        />
      </div>
    </div>
  );
});

interface CheckoutFormProps {
  priceId: string;
  product: typeof PRODUCTS[keyof typeof PRODUCTS];
  initialQuantity?: number;
}

interface CheckoutFormInnerProps extends CheckoutFormProps {
  initialQuantity?: number;
  updatePaymentIntentWithEmail: (email: string, customerInfo?: any) => Promise<any>;
  collectedEmail: string;
  onEmailChange: () => void;
}

interface EmailCollectionStepProps {
  product: typeof PRODUCTS[keyof typeof PRODUCTS];
  initialQuantity?: number;
  onEmailCollected: (email: string) => void;
  initialEmail?: string;
}

// Helper function to extract numeric price from string format
const extractNumericPrice = (priceString: string): number => {
  const numericValue = priceString.replace(/[^0-9.]/g, '');
  return parseFloat(numericValue) || 0;
};

// Helper function to calculate total price for display
const calculateDisplayPrice = (product: typeof PRODUCTS[keyof typeof PRODUCTS], quantity: number, isTeamPlan: boolean): string => {
  const unitPrice = extractNumericPrice(product.price);
  if (isTeamPlan && quantity > 1) {
    const totalPrice = unitPrice * quantity;
    return `$${totalPrice}`;
  }
  return product.price;
};

// Email Collection Step Component using Stripe Link Authentication Element
const EmailCollectionStep = memo(function EmailCollectionStep({
  product,
  initialQuantity = 1,
  onEmailCollected,
  initialEmail
}: EmailCollectionStepProps) {
  const [email, setEmail] = useState(initialEmail || '');
  const [isValidEmail, setIsValidEmail] = useState(!!initialEmail);
  const [isLoading, setIsLoading] = useState(false);

  // Email validation
  const validateEmail = (emailValue: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(emailValue);
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const emailValue = e.target.value;
    setEmail(emailValue);
    setIsValidEmail(validateEmail(emailValue));
  };

  const handleContinue = async () => {
    if (!isValidEmail || !email) return;

    setIsLoading(true);
    try {
      onEmailCollected(email);
    } catch (error) {
      console.error('Error proceeding with email:', error);
      setIsLoading(false);
    }
  };

  const isTeamPlan = initialQuantity > 1;
  const displayPrice = calculateDisplayPrice(product, initialQuantity, isTeamPlan);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-2xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Complete Your Purchase</h1>
          <p className="text-gray-600">Enter your email to continue with {product.name}</p>
        </div>

        {/* Product Summary */}
        <Card className="mb-6">
          <CardContent className="p-6">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="font-semibold text-lg">{product.name}</h3>
                <p className="text-gray-600">{product.description}</p>
                {isTeamPlan && (
                  <p className="text-sm text-gray-500 mt-1">
                    {initialQuantity} user{initialQuantity > 1 ? 's' : ''}
                  </p>
                )}
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold">{displayPrice}</div>
                <div className="text-gray-500">{product.period}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Email Collection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Account Information
            </CardTitle>
            <CardDescription>
              We'll use this email for your account creation and order confirmations
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label htmlFor="email" className="block text-sm font-medium mb-2">
                Email Address *
              </label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={handleEmailChange}
                className={`w-full ${!isValidEmail && email ? 'border-red-500' : ''}`}
              />
              {!isValidEmail && email && (
                <p className="text-red-600 text-sm mt-1">Please enter a valid email address</p>
              )}
            </div>

            <Button
              onClick={handleContinue}
              disabled={!isValidEmail || !email || isLoading}
              className="w-full h-12 text-base font-semibold bg-black hover:bg-gray-800 text-white"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>Continue...</span>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Lock className="h-4 w-4" />
                  <span>Continue to Payment</span>
                </div>
              )}
            </Button>

            <div className="flex items-center justify-center gap-2 text-xs text-gray-500 mt-2">
              <Shield className="h-3 w-3" />
              <span>Secured by Stripe • SSL Encrypted</span>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
});

// Optimized CheckoutForm component using React Hook Form
const OptimizedCheckoutFormInner = memo(function OptimizedCheckoutFormInner({
  product,
  initialQuantity = 1,
  updatePaymentIntentWithEmail,
  collectedEmail,
  onEmailChange
}: CheckoutFormInnerProps) {
  const stripe = useStripe();
  const elements = useElements();
  const router = useRouter();

  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Use React Hook Form for uncontrolled inputs
  const {
    register,
    handleSubmit,
    control,
    watch,
    setValue,
    formState: { errors }
  } = useForm<CheckoutFormWithoutEmailData>({
    resolver: zodResolver(checkoutFormWithoutEmailSchema),
    defaultValues: {
      firstName: '',
      lastName: '',
      addressLine1: '',
      addressLine2: '',
      city: '',
      state: '',
      postalCode: '',
      country: 'US',
      quantity: initialQuantity,
      acceptTerms: false,
      acceptPrivacy: false,
    }
  });

  // Determine if it's a team plan based on initial quantity > 1
  const isTeamPlan = initialQuantity > 1;

  // Watch form values for validation - this only re-renders when values actually change
  const watchedValues = watch();
  const isFormValid = Boolean(
    watchedValues.firstName &&
    watchedValues.lastName &&
    watchedValues.acceptTerms &&
    watchedValues.acceptPrivacy &&
    (!isTeamPlan || (watchedValues.quantity && watchedValues.quantity >= 1))
  );

  // Calculate display price based on quantity
  const displayPrice = calculateDisplayPrice(product, watchedValues.quantity || 1, isTeamPlan);



  const onSubmit = useCallback(async (data: CheckoutFormWithoutEmailData) => {
    if (!stripe || !elements) {
      setError('Payment system not ready. Please try again.');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Update the payment intent with the user's email and information
      // This ensures the payment intent has the correct email for receipts and customer records
      try {
        await updatePaymentIntentWithEmail(collectedEmail, {
          firstName: data.firstName,
          lastName: data.lastName,
          phone: data.phone,
          addressLine1: data.addressLine1,
          addressLine2: data.addressLine2,
          city: data.city,
          state: data.state,
          postalCode: data.postalCode,
          country: data.country,
        });
      } catch (intentError) {
        console.warn('Payment intent creation/update failed:', intentError);
        // Continue with existing payment intent if creation fails
      }

      const { error, paymentIntent } = await stripe.confirmPayment({
        elements,
        confirmParams: {
          return_url: `${window.location.origin}/auth/payment-success?email=${encodeURIComponent(collectedEmail)}&payment_intent={PAYMENT_INTENT_ID}`,
        },
        redirect: 'if_required',
      });

      if (error) {
        setError(error.message || 'Payment failed');
      } else if (paymentIntent?.status === 'succeeded') {
        // Try to automatically log in the user
        try {
          const loginResponse = await fetch('/api/auth/post-payment-login', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              email: collectedEmail,
              paymentIntentId: paymentIntent.id,
            }),
          });

          const loginData = await loginResponse.json();

          if (loginData.success && loginData.loginUrl) {
            // Redirect to the magic link URL for automatic login
            window.location.href = loginData.loginUrl;
          } else {
            // Fallback: redirect to a success page with manual login option
            router.push(`/auth/payment-success?email=${encodeURIComponent(collectedEmail)}&payment_intent=${paymentIntent.id}&payment=success`);
          }
        } catch (loginError) {
          // Fallback: redirect to success page
          router.push(`/auth/payment-success?email=${encodeURIComponent(collectedEmail)}&payment_intent=${paymentIntent.id}&payment=success`);
        }
      } else {
        setError('Payment was not completed. Please try again.');
      }
    } catch (err) {
      setError('An unexpected error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [stripe, elements, router, updatePaymentIntentWithEmail]);

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-start gap-3">
            <div className="flex-shrink-0">
              <div className="w-5 h-5 bg-red-100 rounded-full flex items-center justify-center">
                <span className="text-red-600 text-xs font-bold">!</span>
              </div>
            </div>
            <div className="flex-1">
              <p className="text-red-600 text-sm font-medium mb-1">Payment Error</p>
              <p className="text-red-600 text-sm">{error}</p>
              <button
                type="button"
                onClick={() => setError(null)}
                className="mt-2 text-xs text-red-600 hover:text-red-800 underline"
              >
                Dismiss
              </button>
            </div>
          </div>
        </div>
      )}



      {/* Personal Information */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
          <User className="h-4 w-4" />
          Personal Information
        </div>

        {/* Email Display - Already collected */}
        <div className="bg-gray-50 p-3 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Email Address</label>
              <p className="text-sm text-gray-900">{collectedEmail}</p>
            </div>
            <button
              type="button"
              onClick={onEmailChange}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              Change
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label htmlFor="firstName" className="block text-sm font-medium mb-1">First Name *</label>
            <Input
              id="firstName"
              {...register('firstName')}
            />
            {errors.firstName && (
              <p className="text-red-600 text-sm mt-1">{errors.firstName.message}</p>
            )}
          </div>
          <div>
            <label htmlFor="lastName" className="block text-sm font-medium mb-1">Last Name *</label>
            <Input
              id="lastName"
              {...register('lastName')}
            />
            {errors.lastName && (
              <p className="text-red-600 text-sm mt-1">{errors.lastName.message}</p>
            )}
          </div>
        </div>
      </div>

      {/* Address Information */}
      <div className="space-y-4">
        <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
          <MapPin className="h-4 w-4" />
          Billing Address
        </div>

        <div>
          <label htmlFor="addressLine1" className="block text-sm font-medium mb-1">Address Line 1 *</label>
          <Input
            id="addressLine1"
            {...register('addressLine1')}
          />
          {errors.addressLine1 && (
            <p className="text-red-600 text-sm mt-1">{errors.addressLine1.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="addressLine2" className="block text-sm font-medium mb-1">Address Line 2</label>
          <Input
            id="addressLine2"
            {...register('addressLine2')}
          />
          {errors.addressLine2 && (
            <p className="text-red-600 text-sm mt-1">{errors.addressLine2.message}</p>
          )}
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label htmlFor="city" className="block text-sm font-medium mb-1">City *</label>
            <Input
              id="city"
              {...register('city')}
            />
            {errors.city && (
              <p className="text-red-600 text-sm mt-1">{errors.city.message}</p>
            )}
          </div>
          <div>
            <label htmlFor="state" className="block text-sm font-medium mb-1">State *</label>
            <Controller
              name="state"
              control={control}
              rules={{ required: 'State is required' }}
              render={({ field }) => (
                <Select onValueChange={field.onChange} value={field.value}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select state" />
                  </SelectTrigger>
                  <SelectContent>
                    {US_STATES.map((state) => (
                      <SelectItem key={state.value} value={state.value}>
                        {state.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
            {errors.state && (
              <p className="text-red-600 text-sm mt-1">{errors.state.message}</p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div>
            <label htmlFor="postalCode" className="block text-sm font-medium mb-1">Postal Code *</label>
            <Input
              id="postalCode"
              {...register('postalCode')}
            />
            {errors.postalCode && (
              <p className="text-red-600 text-sm mt-1">{errors.postalCode.message}</p>
            )}
          </div>
          <div>
            <label htmlFor="country" className="block text-sm font-medium mb-1">Country *</label>
            <select
              id="country"
              {...register('country')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="US">United States</option>
              <option value="CA">Canada</option>
              <option value="GB">United Kingdom</option>
              <option value="AU">Australia</option>
            </select>
            {errors.country && (
              <p className="text-red-600 text-sm mt-1">{errors.country.message}</p>
            )}
          </div>
        </div>
      </div>

      {/* Team Plan Quantity */}
      {isTeamPlan && (
        <div className="space-y-4">
          <div className="flex items-center gap-2 text-sm font-medium text-gray-700">
            <Users className="h-4 w-4" />
            Team Size
          </div>

          <div>
            <label htmlFor="quantity" className="block text-sm font-medium mb-1">Number of Users *</label>
            <Input
              id="quantity"
              type="number"
              min="1"
              {...register('quantity', { valueAsNumber: true })}
            />
            {errors.quantity && (
              <p className="text-red-600 text-sm mt-1">{errors.quantity.message}</p>
            )}
          </div>
        </div>
      )}

      {/* Payment Information */}
      <IsolatedPaymentElement formData={watchedValues} />

      {/* Terms and Privacy */}
      <div className="space-y-3">
        <div className="flex items-center space-x-2">
          <Controller
            name="acceptTerms"
            control={control}
            rules={{ required: 'You must accept the terms of service' }}
            render={({ field }) => (
              <Checkbox
                id="acceptTerms"
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            )}
          />
          <label htmlFor="acceptTerms" className="text-sm">
            I accept the <a href="/terms" className="text-blue-600 hover:underline">Terms of Service</a> *
          </label>
        </div>
        {errors.acceptTerms && (
          <p className="text-red-600 text-sm">{errors.acceptTerms.message}</p>
        )}

        <div className="flex items-center space-x-2">
          <Controller
            name="acceptPrivacy"
            control={control}
            rules={{ required: 'You must accept the privacy policy' }}
            render={({ field }) => (
              <Checkbox
                id="acceptPrivacy"
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            )}
          />
          <label htmlFor="acceptPrivacy" className="text-sm">
            I accept the <a href="/privacy" className="text-blue-600 hover:underline">Privacy Policy</a> *
          </label>
        </div>
        {errors.acceptPrivacy && (
          <p className="text-red-600 text-sm">{errors.acceptPrivacy.message}</p>
        )}
      </div>

      {/* Submit Button */}
      <Button
        type="submit"
        className="w-full h-12 text-base font-semibold bg-black hover:bg-gray-800 text-white transition-colors duration-200"
        disabled={!stripe || !elements || isLoading || !isFormValid}
      >
        <div className="flex items-center justify-center gap-2">
          {isLoading ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              <span>Processing Payment...</span>
            </>
          ) : (
            <>
              <Lock className="h-4 w-4" />
              <span>Complete Purchase - {displayPrice}</span>
            </>
          )}
        </div>
      </Button>

      {/* Security Badge */}
      <div className="flex items-center justify-center gap-2 text-xs text-gray-500 mt-2">
        <Shield className="h-3 w-3" />
        <span>Secured by Stripe • SSL Encrypted</span>
      </div>
    </form>
  );
});

// Wrapper component that handles clientSecret creation and Elements provider
function CheckoutForm({ priceId, product, initialQuantity }: CheckoutFormProps) {
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [user, setUser] = useState<{ id?: string; email?: string; user_metadata?: { full_name?: string } } | null>(null);
  // Removed unused isLoading state
  const supabase = useSupabaseClient();

  const isTeamPlan = TEAM_PRICE_IDS.includes(priceId);

  useEffect(() => {
    // Get current user
    const getUser = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      setUser(user);
    };
    getUser();
  }, [supabase]);

  // Create payment intent function - initially called without email for Stripe Elements setup
  // Following Stripe best practices: email is optional for initial payment intent creation
  const createPaymentIntent = useCallback(async (email?: string, customerInfo?: any) => {
    try {
      const response = await fetch('/api/checkout/create-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceId,
          userId: user?.id,
          email: email,
          quantity: isTeamPlan ? 1 : undefined,
          customerInfo: customerInfo || {
            firstName: '',
            lastName: '',
            addressLine1: '',
            addressLine2: '',
            city: '',
            state: '',
            postalCode: '',
            country: 'US',
          },
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create payment intent');
      }

      const data = await response.json();
      setClientSecret(data.clientSecret);
      return data.clientSecret;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to initialize payment. Please try again.';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [priceId, user, isTeamPlan]);

  // Function to update payment intent with email when user fills out form
  const updatePaymentIntentWithEmail = useCallback(async (email: string, customerInfo?: any) => {
    if (!clientSecret) {
      console.log('No client secret available for payment intent update');
      return;
    }

    // Extract payment intent ID from client secret
    const paymentIntentId = clientSecret.split('_secret_')[0];

    try {
      const response = await fetch('/api/checkout/update-payment-intent', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          paymentIntentId,
          email,
          customerInfo,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update payment intent');
      }

      const data = await response.json();
      console.log('✅ Payment intent updated with email:', data);
      return data;
    } catch (error) {
      console.error('❌ Failed to update payment intent with email:', error);
      throw error;
    }
  }, [clientSecret]);

  // State to track email collection for payment intent creation
  const [collectedEmail, setCollectedEmail] = useState<string | null>(user?.email || null);
  const [isEmailCollected, setIsEmailCollected] = useState<boolean>(!!user?.email);

  // Create payment intent only after email is collected
  // This aligns with our Supabase Magic Link authentication requirements
  useEffect(() => {
    if (priceId && !clientSecret && collectedEmail && isEmailCollected) {
      console.log('Creating payment intent with collected email:', collectedEmail);
      createPaymentIntent(collectedEmail).catch((error) => {
        console.log('Payment intent creation failed, will retry:', error);
      });
    }
  }, [priceId, clientSecret, collectedEmail, isEmailCollected, createPaymentIntent]);

  if (error) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="text-red-600 mb-2">{error}</div>
          <button
            onClick={() => window.location.reload()}
            className="text-blue-600 hover:underline"
          >
            Try again
          </button>
        </div>
      </div>
    );
  }

  // Show email collection if no email is collected yet
  if (!isEmailCollected) {
    return <EmailCollectionStep
      product={product}
      initialQuantity={initialQuantity}
      onEmailCollected={(email: string) => {
        setCollectedEmail(email);
        setIsEmailCollected(true);
      }}
      initialEmail={user?.email}
    />;
  }

  if (!clientSecret) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="flex items-center gap-2 justify-center">
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            <span>Initializing payment...</span>
          </div>
        </div>
      </div>
    );
  }

  return <StripeElementsWrapper
    clientSecret={clientSecret}
    priceId={priceId}
    product={product}
    initialQuantity={initialQuantity}
    updatePaymentIntentWithEmail={updatePaymentIntentWithEmail}
    collectedEmail={collectedEmail!}
    onEmailChange={() => {
      setIsEmailCollected(false);
      setCollectedEmail(null);
    }}
  />;
}

// Separate wrapper component to isolate Stripe Elements from parent re-renders
const StripeElementsWrapper = memo(function StripeElementsWrapper({
  clientSecret,
  priceId,
  product,
  initialQuantity,
  updatePaymentIntentWithEmail,
  collectedEmail,
  onEmailChange
}: {
  clientSecret: string | null;
  priceId: string;
  product: typeof PRODUCTS[keyof typeof PRODUCTS];
  initialQuantity?: number;
  updatePaymentIntentWithEmail: (email: string, customerInfo?: any) => Promise<any>;
  collectedEmail: string;
  onEmailChange: () => void;
}) {
  const elementsOptions = useMemo(() => {
    if (!clientSecret) return null;

    return {
      clientSecret,
      appearance: {
        theme: 'stripe' as const,
        variables: {
          colorPrimary: '#000000',
          colorBackground: '#ffffff',
          colorText: '#30313d',
          colorDanger: '#df1b41',
          fontFamily: '"Segoe UI", Roboto, sans-serif',
          spacingUnit: '4px',
          borderRadius: '6px',
          // Enhanced mobile experience
          fontSizeBase: '16px', // Prevents zoom on iOS
          fontSizeSm: '14px',
        },
      },
      loader: 'auto' as const,
      // Enable payment method ordering for better conversion
      paymentMethodOrder: ['card', 'link', 'apple_pay', 'google_pay'],
    };
  }, [clientSecret]);

  if (!elementsOptions) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <div className="flex items-center gap-2 justify-center">
            <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            <span>Initializing payment...</span>
          </div>
        </div>
      </div>
    );
  }

  return (
    <Elements
      stripe={stripePromise}
      options={elementsOptions}
    >
      <OptimizedCheckoutFormInner
        priceId={priceId}
        product={product}
        initialQuantity={initialQuantity}
        updatePaymentIntentWithEmail={updatePaymentIntentWithEmail}
        collectedEmail={collectedEmail}
        onEmailChange={onEmailChange}
      />
    </Elements>
  );
});

export default function CheckoutPage() {
  // Use the optimized checkout page with React Hook Form
  return <CheckoutPageOriginal />;
}

function CheckoutPageOriginal() {
  const params = useParams();
  const searchParams = useSearchParams();
  const priceId = params.priceId as string;
  const quantityParam = searchParams.get('quantity');
  const initialQuantity = quantityParam ? parseInt(quantityParam, 10) : 1;

  const product = PRODUCTS[priceId as keyof typeof PRODUCTS];

  if (!product) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Invalid Product</CardTitle>
            <CardDescription>
              The requested product could not be found.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button asChild className="w-full">
              <Link href="/pricing">Back to Pricing</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="px-4 lg:px-6 h-14 flex items-center border-b">
        <Link href="/" className="flex items-center justify-center">
          <Image src="/QuantBoost_LogoOnly_v0.png" alt="QuantBoost Logo" width={32} height={32} />
          <span className="ml-2 text-lg font-semibold">QuantBoost</span>
        </Link>
        <nav className="ml-auto">
          <Button variant="ghost" asChild>
            <Link href="/pricing">← Back to Pricing</Link>
          </Button>
        </nav>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-4 sm:py-8 max-w-7xl">
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 lg:gap-8">
          {/* Product Summary - Sticky Sidebar */}
          <div className="lg:col-span-2 order-2 lg:order-1">
            <div className="lg:sticky lg:top-8">
              <h1 className="text-2xl sm:text-3xl font-bold mb-4 sm:mb-6">Complete Your Purchase</h1>

              <Card className="mb-6">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
                      <span className="text-primary-foreground font-bold text-sm">QB</span>
                    </div>
                    {product.name}
                  </CardTitle>
                  <CardDescription>{product.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div className="text-center p-4 bg-muted/50 rounded-lg">
                      <div className="text-4xl font-bold text-primary">{product.price}</div>
                      <div className="text-sm text-muted-foreground">{product.period}</div>
                    </div>

                    <div>
                      <h3 className="font-semibold mb-3 flex items-center gap-2">
                        <Shield className="h-4 w-4 text-primary" />
                        Included Features:
                      </h3>
                      <ul className="space-y-2">
                        {product.features.map((feature, index) => (
                          <li key={index} className="flex items-center gap-3 text-sm">
                            <div className="w-2 h-2 bg-primary rounded-full flex-shrink-0"></div>
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    <div className="border-t pt-4">
                      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                        <Lock className="h-4 w-4" />
                        <span>30-day money-back guarantee</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                        <Shield className="h-4 w-4" />
                        <span>Cancel anytime</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                        <CreditCard className="h-4 w-4" />
                        <span>Secure payment processing</span>
                      </div>
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Shield className="h-4 w-4" />
                        <span>Stripe Secure</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Trust Indicators */}
              <div className="text-center space-y-3">
                <div className="flex items-center justify-center gap-3 text-xs text-muted-foreground flex-wrap">
                  <div className="flex items-center gap-1">
                    <Shield className="h-3 w-3" />
                    <span>256-bit SSL</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Lock className="h-3 w-3" />
                    <span>PCI DSS</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <CreditCard className="h-3 w-3" />
                    <span>Stripe Secure</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Image
                      src="/stripe-link-logo.svg"
                      alt="Stripe Link"
                      width={16}
                      height={8}
                      className="opacity-70"
                    />
                    <span>One-click</span>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">
                  Trusted by thousands of professionals worldwide
                </p>
              </div>
            </div>
          </div>

          {/* Checkout Form */}
          <div className="lg:col-span-3 order-1 lg:order-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-xl sm:text-2xl">Secure Checkout</CardTitle>
                <CardDescription>
                  Complete your information below to finalize your purchase
                </CardDescription>
              </CardHeader>
              <CardContent>
                <CheckoutForm priceId={priceId} product={product} initialQuantity={initialQuantity} />
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}