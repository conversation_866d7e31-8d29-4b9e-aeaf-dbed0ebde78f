// Test script with realistic Stripe webhook events that don't require external API calls
const crypto = require('crypto');

// Test checkout session completed with realistic data
async function testRealisticCheckoutSession() {
  const webhookSecret = 'whsec_7f9a4f74382639cea4d3dd0844e14acb61c4534165edcc6bc21a051f5537ce0f';
  const endpointUrl = 'http://localhost:3000/api/webhooks/stripe';
  
  // Create a realistic checkout session event that doesn't require Stripe API calls
  const testEvent = {
    id: 'evt_realistic_checkout_' + Date.now(),
    object: 'event',
    type: 'checkout.session.completed',
    created: Math.floor(Date.now() / 1000),
    data: {
      object: {
        id: 'cs_realistic_' + Date.now(),
        object: 'checkout.session',
        mode: 'subscription',
        customer: 'cus_realistic_customer_' + Date.now(),
        customer_details: {
          email: '<EMAIL>'
        },
        customer_email: '<EMAIL>',  // Fallback email field
        subscription: 'sub_realistic_' + Date.now()
      }
    }
  };

  const payload = JSON.stringify(testEvent);
  const timestamp = Math.floor(Date.now() / 1000);
  
  const signedPayload = timestamp + '.' + payload;
  const signature = crypto
    .createHmac('sha256', webhookSecret)
    .update(signedPayload, 'utf8')
    .digest('hex');
  
  const stripeSignature = `t=${timestamp},v1=${signature}`;

  console.log('🧪 Testing REALISTIC checkout.session.completed...');
  console.log('📦 Event ID:', testEvent.id);
  console.log('📧 Customer Email:', testEvent.data.object.customer_details.email);
  console.log('🆔 Customer ID:', testEvent.data.object.customer);

  try {
    const response = await fetch(endpointUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'stripe-signature': stripeSignature
      },
      body: payload
    });

    console.log('📊 Response Status:', response.status);
    const responseText = await response.text();
    console.log('📄 Response Body:', responseText);

    if (response.ok) {
      console.log('✅ Realistic checkout session test PASSED');
      return true;
    } else {
      console.log('❌ Realistic checkout session test FAILED');
      return false;
    }
  } catch (error) {
    console.error('💥 Error testing realistic checkout session:', error);
    return false;
  }
}

// Test subscription created with complete data
async function testRealisticSubscriptionCreated() {
  const webhookSecret = 'whsec_7f9a4f74382639cea4d3dd0844e14acb61c4534165edcc6bc21a051f5537ce0f';
  const endpointUrl = 'http://localhost:3000/api/webhooks/stripe';
  
  // First, we need to create a profile that the subscription can reference
  // This simulates the checkout.session.completed event creating the profile first
  
  const customerId = 'cus_realistic_sub_' + Date.now();
  const subscriptionId = 'sub_realistic_' + Date.now();
  
  const testEvent = {
    id: 'evt_realistic_sub_' + Date.now(),
    object: 'event',
    type: 'customer.subscription.created',
    created: Math.floor(Date.now() / 1000),
    data: {
      object: {
        id: subscriptionId,
        object: 'subscription',
        customer: customerId,
        status: 'active',
        items: {
          data: [{
            price: {
              id: 'price_1RCQeBE6FvhUKV1bUN94Oihf'  // Quarterly price
            },
            quantity: 1
          }]
        },
        current_period_start: Math.floor(Date.now() / 1000),
        current_period_end: Math.floor(Date.now() / 1000) + (90 * 24 * 60 * 60), // 90 days
        created: Math.floor(Date.now() / 1000),
        cancel_at_period_end: false,
        canceled_at: null,
        trial_start: null,
        trial_end: null
      }
    }
  };

  const payload = JSON.stringify(testEvent);
  const timestamp = Math.floor(Date.now() / 1000);
  
  const signedPayload = timestamp + '.' + payload;
  const signature = crypto
    .createHmac('sha256', webhookSecret)
    .update(signedPayload, 'utf8')
    .digest('hex');
  
  const stripeSignature = `t=${timestamp},v1=${signature}`;

  console.log('\n🧪 Testing REALISTIC customer.subscription.created...');
  console.log('📦 Event ID:', testEvent.id);
  console.log('🆔 Customer ID:', customerId);
  console.log('📋 Subscription ID:', subscriptionId);
  console.log('💰 Price ID:', testEvent.data.object.items.data[0].price.id);

  try {
    const response = await fetch(endpointUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'stripe-signature': stripeSignature
      },
      body: payload
    });

    console.log('📊 Response Status:', response.status);
    const responseText = await response.text();
    console.log('📄 Response Body:', responseText);

    if (response.ok) {
      console.log('✅ Realistic subscription created test PASSED');
      return true;
    } else {
      console.log('❌ Realistic subscription created test FAILED');
      return false;
    }
  } catch (error) {
    console.error('💥 Error testing realistic subscription:', error);
    return false;
  }
}

// Test idempotency - send the same event twice
async function testIdempotency() {
  const webhookSecret = 'whsec_7f9a4f74382639cea4d3dd0844e14acb61c4534165edcc6bc21a051f5537ce0f';
  const endpointUrl = 'http://localhost:3000/api/webhooks/stripe';
  
  const eventId = 'evt_idempotency_test_' + Date.now();
  
  const testEvent = {
    id: eventId,  // Same ID for both requests
    object: 'event',
    type: 'checkout.session.completed',
    created: Math.floor(Date.now() / 1000),
    data: {
      object: {
        id: 'cs_idempotency_' + Date.now(),
        object: 'checkout.session',
        mode: 'subscription',
        customer: 'cus_idempotency_' + Date.now(),
        customer_details: {
          email: '<EMAIL>'
        },
        subscription: 'sub_idempotency_' + Date.now()
      }
    }
  };

  const payload = JSON.stringify(testEvent);
  const timestamp = Math.floor(Date.now() / 1000);
  
  const signedPayload = timestamp + '.' + payload;
  const signature = crypto
    .createHmac('sha256', webhookSecret)
    .update(signedPayload, 'utf8')
    .digest('hex');
  
  const stripeSignature = `t=${timestamp},v1=${signature}`;

  console.log('\n🧪 Testing IDEMPOTENCY protection...');
  console.log('📦 Event ID:', eventId);

  try {
    // First request
    console.log('📤 Sending first request...');
    const response1 = await fetch(endpointUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'stripe-signature': stripeSignature
      },
      body: payload
    });

    console.log('📊 First Response Status:', response1.status);
    const responseText1 = await response1.text();
    console.log('📄 First Response Body:', responseText1);

    // Second request (should be detected as duplicate)
    console.log('📤 Sending duplicate request...');
    const response2 = await fetch(endpointUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'stripe-signature': stripeSignature
      },
      body: payload
    });

    console.log('📊 Second Response Status:', response2.status);
    const responseText2 = await response2.text();
    console.log('📄 Second Response Body:', responseText2);

    // Check if idempotency worked
    if (response1.ok && response2.ok && responseText2.includes('already processed')) {
      console.log('✅ Idempotency test PASSED');
      return true;
    } else {
      console.log('❌ Idempotency test FAILED');
      return false;
    }
  } catch (error) {
    console.error('💥 Error testing idempotency:', error);
    return false;
  }
}

// Run realistic tests
async function runRealisticTests() {
  console.log('🚀 Starting REALISTIC webhook endpoint tests...\n');
  
  const test1 = await testRealisticCheckoutSession();
  const test2 = await testRealisticSubscriptionCreated();
  const test3 = await testIdempotency();
  
  console.log('\n📊 Realistic Test Results:');
  console.log('✅ Checkout session test:', test1 ? 'PASSED' : 'FAILED');
  console.log('✅ Subscription created test:', test2 ? 'PASSED' : 'FAILED');
  console.log('✅ Idempotency test:', test3 ? 'PASSED' : 'FAILED');
  
  const allPassed = test1 && test2 && test3;
  
  if (allPassed) {
    console.log('\n🎉 All REALISTIC webhook tests PASSED!');
    console.log('🚀 The webhook system is ready for production deployment.');
    console.log('📋 Next steps:');
    console.log('   1. Fix Azure Static Web Apps deployment');
    console.log('   2. Update Stripe webhook endpoint to staging URL');
    console.log('   3. Test complete payment flow end-to-end');
  } else {
    console.log('\n❌ Some REALISTIC tests FAILED. Check the logs above for details.');
  }
  
  return allPassed;
}

runRealisticTests();
