const path = require('path');

module.exports = async () => {
  console.log('Executing globalSetup.js...');
  require('dotenv').config({ path: path.resolve(__dirname, '../.env.test'), override: true });
  console.log('STRIPE_SECRET_KEY in globalSetup:', process.env.STRIPE_SECRET_KEY ? 'Loaded' : 'Not Loaded');
  // You can set global variables here if needed, e.g.:
  // process.env.GLOBAL_VAR_FOR_TESTS = 'some_value';
};
